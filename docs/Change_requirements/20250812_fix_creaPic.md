# 需求 [add_stat4reImport]

## 反馈

1. Maggie反馈creaReso的房源图片url生成逻辑存在问题,不应该使用sid。
2. Fred反馈数据库中的`phoP`字段不应该添加`/`

## 需求提出人:    Fred/Maggie

## 修改人：       Lu<PERSON>owei

## 提出日期:      2025-08-12

## 原因

1. creaReso房源的sid字段是`ListingId`映射得到,但是图片url使用的是`ListingKey`字段。

## 解决办法

1. 图片url的生成添加判断,creaReso房源使用`ListingKey`,而不是sid字段
2. 使用`phoP`时添加判断是否以`/`开始,如果不是添加`/`

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-08-12

## online-step

1. 重新init es watch
2. 重启server