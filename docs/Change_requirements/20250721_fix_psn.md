# 需求 [fix_psn]

## 反馈

1. Sales反馈在搜房源的时候，交接日期 我搜immed 然后什么房源都没有了

## 需求提出人:    Rain/Tao

## 修改人：       Lu<PERSON>

## 提出日期:      2025-07-19

## 原因

1. 房源对`psn`检索时使用的是`rmPsn`字段,`rmPsn`字段只有在以前的`TREB`房源import时生成。新的board没有生成该字段
2. trebReso房源`PossessionType`字段和`PossessionDetails`字段都可以计算`rmPsn`,现在只用了`PossessionDetails`

## 解决办法

1. 房源import时添加判断,如果存在`psn`,`poss_type`字段,生成`rmPsn`相关字段
2. 添加batch修复`properties`表中存在`psn`,`poss_type`字段,没有`rmPsn`字段的数据

## 影响范围

1. 房源`rmPsn`,`rmPsnDate`字段的生成。
2. 房源筛选。

## 是否需要补充UT

1. 需要补充/修复UT

## 确认日期:    2025-07-21

## online-step

1. 重启watch&import
2. 执行batch,修复历史数据
```shell
./start.sh -n fix_psn -cmd "lib/batchBase.coffee batch/prop/fix_psn.coffee dryrun"

测试服执行结果:
Completed, Total process time 1.32535 mins, processed(1193443):900.4K/m, noUpdate(755294):569.8K/m, dryRun(438149):330.5K/m
```
