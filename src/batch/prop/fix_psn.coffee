###
Description:    修复房源PSN字段问题，为存在psn字段但缺少rmPsn字段的房源生成rmPsn和rmPsnDate字段
Usage:          ./start.sh -n fix_psn -cmd "lib/batchBase.coffee batch/prop/fix_psn.coffee dryrun"
Create date:    2025-07-21
Author:         xiaoweiLuo
Run frequency:  Once
###

debug = DEBUG()
speed = INCLUDE 'lib.speed'
helpers = INCLUDE 'lib.helpers'
{genRmPsn} = INCLUDE 'libapp.properties'
PropertiesCol = COLLECTION 'vow', 'properties'

speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 5000,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}

dryRun = AVGS.indexOf('dryrun') >= 0
startTs = new Date()

main = () ->
  # 查询存在psn字段但缺少rmPsn字段的房源
  query = {
    src: {$ne: 'RM'},
    rmPsn: null,
    $or:[
      {psn: {$ne: null}},
      {poss_type: {$ne: null}}
    ]
  }
  projection = {
    psn: 1
    onD: 1
    poss_type: 1
  }
  
  try
    cur = await PropertiesCol.find query, {projection}
  catch err
    debug.error 'Failed to create cursor', err
    return EXIT 1, err
    
  cur.maxTimeMS 3600000 # 设置查询超时时间为1小时
  stream = cur.stream()
  
  obj =
    verbose: 1
    stream: stream
    high: 30
    end: (err) ->
      processTs = (new Date().getTime() - startTs.getTime())/(1000*60)
      if err
        debug.error "Error exit. Total process time #{processTs} mins. ", speedMeter.toString()
        return EXIT 1, err
      debug.info "Completed, Total process time #{processTs} mins, #{speedMeter.toString()}"
      EXIT 0
    process: (prop, cb) ->
      speedMeter.check { processed: 1 }

      # 检查psn/poss_type字段是否存在
      if (not prop.psn?.toString()) and (not prop.poss_type?.toString())
        debug.debug "propId: #{prop._id} psn and poss_type field is empty or null"
        speedMeter.check { emptyPsn: 1 }
        return cb()

      update = {noModifyMt: true}

      psnStr = prop.psn?.toString() or ''
      psnStr += ',' + prop.poss_type.toString() if prop.poss_type
      {rmPsn, rmPsnDate} = genRmPsn {onD:prop.onD,psn:psnStr}
      
      # 设置rmPsn字段
      if rmPsn?.length > 0
        update.$set ?= {}
        update.$set.rmPsn = rmPsn
        debug.debug "propId: #{prop._id} will set rmPsn to:", rmPsn
      
      # 设置rmPsnDate字段
      if rmPsnDate
        update.$set ?= {}
        update.$set.rmPsnDate = rmPsnDate
        debug.debug "propId: #{prop._id} will set rmPsnDate to:", rmPsnDate

      # 只有在有实际更新内容时才执行更新
      if not (update.$set)
        debug.debug "propId: #{prop._id} no update needed"
        speedMeter.check { noUpdate: 1 }
        return cb()
        
      if dryRun
        debug.info "propId:#{prop._id}, update:",update
        speedMeter.check { dryRun: 1 }
        return cb()

      try
        await PropertiesCol.updateOne {_id: prop._id}, update
        speedMeter.check { updated: 1 }
        debug.debug "propId: #{prop._id} updated successfully"
      catch err
        debug.error "Failed to update prop #{prop._id}", err
        speedMeter.check { error: 1 }

      return cb()

  helpers.streaming obj
      
main()
