config = CONFIG(['serverBase','share'])
i18n = INCLUDE 'lib.i18n'
libP = INCLUDE 'libapp.properties'
{getQueryReadable} = INCLUDE 'libapp.propSearchReadable'

libProject = INCLUDE 'libapp.project'
{setPropWebUrl,blurProp} = INCLUDE 'libapp.propWeb'
libPropertyImage = INCLUDE 'libapp.propertyImage'
propSearch = INCLUDE 'libapp.propSearch'
statHelper = INCLUDE 'libapp.stat_helper'
# propCommons = INCLUDE 'libapp.propCommons'
Properties = MODEL 'Properties'
Project = MODEL 'Project'
libUser = INCLUDE 'libapp.user'
ProvAndCity = MODEL 'ProvAndCity'
checksum = INCLUDE 'lib.checksum'
# propSearchSql = INCLUDE 'libapp.propSearchSql'
propStatsModel = MODEL 'PropStats'
# SysData = COLLECTION 'chome', 'sysdata'
User = COLLECTION 'chome','user'
CrmModel = MODEL 'Crm'
ShortUrl = MODEL 'ShortUrl'
EstimatedModel = MODEL 'Estimated'
{listingPicUrlReplace} = INCLUDE 'libapp.common'
{respError} = INCLUDE 'libapp.responseHelper'
getExchangeRates = DEF 'getExchangeRates'
getExchangeNames = DEF 'getExchangeNames'
# fnIsRealtor = DEF 'isRealtor'
UserModel = MODEL 'User'
getConfig = DEF 'getConfig'
{renderSavedSearchTxt} = INCLUDE 'libapp.savedSearch'
{buildMapboxMapUrl} = INCLUDE 'libapp.boundaryHelper'
{setNeedLogin} = INCLUDE 'libapp.filterProperties'
GroupModel = MODEL 'Group'
InRealNoteModel = MODEL 'InRealNoteInst'
PersonalNoteModel = MODEL 'PersonalNoteInst'
PropFavouriteModel = MODEL 'PropFavourite'
ClaimModel = MODEL 'Claim'
ObjectId = INCLUDE('lib.mongo4').ObjectId
TokenModel = MODEL 'Token'

# 固定城市加上翻译过的城市 ON:79,BC:18,AB:,3,NB:7,NL:1,NS:2,PE:4
HOT_CITIES_LIMIT = 100
MAX_SAVEDSEARCH_PROP_LIMIT = 7000

# getCitiesByProv = DEF 'getCitiesByProv'
# getCmtyByProvAndCity = DEF 'getCmtyByProvAndCity'
# getAllTranslatedProvs = DEF 'getAllTranslatedProvs'
# getAllTranslatedPtype2s = DEF 'getAllTranslatedPtype2s'

# updateListingStatus = DEF 'updateListingStatus'
# needLoginToViewProp = propSearch.needLoginToViewProp
dataMethods = DEF 'dataMethods'
# MINIAPP_POP_CITIES = DEF 'MINIAPP_POP_CITIES'
MAP_PROP_FIELDS = DEF 'MAP_PROP_FIELDS'
# LIST_PROP_FIELDS = DEF 'LIST_PROP_FIELDS'
getNextTopListings = DEF 'getNextTopListings'

helpers = INCLUDE 'lib.helpers'
debug = DEBUG()

propTranslate = INCLUDE 'libapp.propertiesTranslate'
rateLimiter = INCLUDE 'lib.rateLimiterV2'
PropPicLimiterInstance = rateLimiter.createLimiter {AccessPerSessionLimit:30,source:'propPicUrls'}
PropPicLimiterFilter = rateLimiter.createFilter PropPicLimiterInstance

# NOTE: 房源检索添加爬虫检测
PropSearchLimiterInstance = rateLimiter.createLimiter {ScraperDetectionEnabled:true,source:'propSearch'}
PropSearchLimiterFilter = rateLimiter.createFilter PropSearchLimiterInstance
#propListQuery = propSearchSql.query

# translateConfig = {
#   clear:false
# }
# DEF 'translateConfig',translateConfig
# propConfg = {
#   showSoldPrice:false
#   isPaytopAll:false
# }
# DEF 'propConfg',propConfg
# '/calc/exchange.json'


redirectLogin = ({resp,state,lang,isWeb})->
  if isWeb
    redirect_uri = '/www/login'
  else
    redirect_uri = '/1.5/user/login'
  redirect_uri += "?state=#{state}" if state
  redirect_uri += "&lang=#{lang}" if lang
  resp.redirect redirect_uri

DEF 'redirectLogin',redirectLogin

getPropAdrltr = DEF 'getPropAdrltr'
TOP_LISTING_LIMIT = 5 
# map/list search
# 获取房源列表
# @param {Object} req - 请求对象
# @param {Object} resp - 响应对象  
# @param {Object} user - 用户对象
# @param {Object} params - 查询参数
# @param {Function} cb - 回调函数
getPropList = (req, resp, user, params = {}, cb) ->
  try
    # 检查用户是否在inreal group中
    isRealGroup = await GroupModel.isInGroup {
      uid: user?._id
      groupName: ':inReal'
    }
  catch err
    debug.error 'GroupModel.isInGroup', err

  # 获取基础查询选项并合并参数
  params = {
    ...params
    ...libP.getBaseForPropList(req, user)
    isRealGroup
  }

  # 过滤搜索参数
  libP.filterSearchParams params

  # 设置多语言
  req.setupL10n() unless req._ab and req._

  # 处理微信相关参数
  hasWechat = if params?.hasWechat? then params.hasWechat else true
  hasWechat = hasWechat and user.hasWechat if user?.hasWechat?

  # 处理项目类型查询
  if params.src is 'rm' and (params.ptype is 'Project' or params.ltp is 'projects')
    try
      ret = await getRMProjects req, user, params
    catch err
      ret['items'] = []
      ret.err = err.toString()
      debug.error 'getRMProjects', err
    libP.setAdrltr2Prop(ret) if ret.ul?.length > 0
    return cb null, ret

  # 清理地址字符串
  params.addr = params.addr.replace(/[\/\-\']/g, '').trim() if params.addr

  try
    # 查询房源列表
    ret = await Properties.findListings {
      params
      isRealtor: req.hasRole 'realtor'
    }
  catch err
    debug.error 'Properties.getPropList', err, req.remoteIP()
    return cb(err)
  # 如果有搜索结果且需要置顶房源,处理置顶房源
  # native没有needToplisting参数，判断是否搜索bbox来确认
  needToplisting = params.needToplisting or params.bbox
  if ret.result?.length and needToplisting
    # 获取置顶房源列表
    selectedTopListings = if params.notApp then [] else (getNextTopListings(params) or [])
    
    # 将置顶房源转为Map结构,方便查找
    topListingMap = selectedTopListings.reduce((map, listing) ->
      map[listing._id] = listing
      map
    , {})

    # 分离普通房源和置顶房源
    {toplistingFromRet, nonTopListing} = ret.result.reduce((acc, listing) ->
      if new Date(listing.topTs) > new Date()
        # 如果在置顶Map中存在,使用Map中的数据
        if cached = topListingMap[listing._id]
          acc.toplistingFromRet.push(cached)
          # 从selectedTopListings中移除重复项
          idx = selectedTopListings.findIndex((p) -> p._id is cached._id)
          selectedTopListings.splice(idx, 1)
        else
          # 不在Map中则添加adrltr属性
          listing.adrltr = getPropAdrltr(listing)
          acc.toplistingFromRet.push(listing)
      else
        acc.nonTopListing.push(listing)
      acc
    , {toplistingFromRet: [], nonTopListing: []})

    # 如果置顶房源不足5个,从selectedTopListings中补充

    if (count = TOP_LISTING_LIMIT - toplistingFromRet.length) > 0
      toplistingFromRet = toplistingFromRet.concat(selectedTopListings.slice(0, count))

    # 合并置顶和普通房源
    ret.result = toplistingFromRet.concat(nonTopListing)
  # 翻译房源列表并处理数量限制
  ret.items = propTranslate.translate_rmprop_list(req, ret.result)
  ret.cnt = ret.items.length if ret.items.length > ret.cnt
  # savessearch 保存之前需要统计匹配的房源，只判断数量，不需要其他字段
  if params.checkMaxCnt
    msg = null
    if ret.cnt > MAX_SAVEDSEARCH_PROP_LIMIT
      msg = req.l10n('Matched too many listings, please narrow your criteria.')
    return cb null, {msg}

  # NOTE: thumbUrl with {ml_base} in prop need to replace
  libP.thumbUrlListReplace(ret.items, params.isCip, config.share?.hostNameCn)

  # 处理旧版本的状态显示
  if not req.verGTE('6.5.2')
    for p in ret.items
      if p.lst in ['Pnd','Cld']
        p.lst = if /rent|lease/ig.test(p.saletp) then 'Lsd' else 'Sld'

  # 添加收藏字段
  if ret.items?.length and (not params.skipFav)
    try
      ret.items = await Properties.addPropFavFields {props:ret.items,uid:user?._id}
    catch err
      debug.error err,'getPropList'

  # 处理每个房源的额外信息
  for p in ret.items
    # 设置登录相关属性
    libP.setUpPropLogin(params.isShare, user, p)
    req.setupL10n() unless req._ab
    
    # 根据权限处理地址信息
    if not hasWechat
      delete p.adrltr

    # 添加额外字段
    Properties.addPropAdditionalFields({
      params,
      user,
      prop:p,
      isAllowedPropAdmin:params.isPropAdmin, 
      isAllowedVipUser:params.isVipUser,
      l10n:(a,b)->req.l10n(a,b),
      _ab:(a,b)->req._ab(a,b),
      apisrc:params.apisrc,
      isRealGroup,
      simplfyPtp:false
    })
    
    setPropWebUrl(req,p)

    if 'app' isnt req.getDevType()
      # 生成图片URL
      # libP.genListingPicReplaceUrls(p, params.isCip, {
      #   isPad:params.isPad,
      #   shareHostNameCn:config.shareHostNameCn,
      #   isThumb:1
      # })
      # p.picUrls = listingPicUrlReplace(p)
      blurProp({prop:p,MSG_STRINGS}) if p.login
    
    # 删除私有字段
    Properties.deletePropPriviteFields({user,prop:p,isListData:true})

  delete ret.result

  # 处理MLS和非微信用户的权限
  if (params.mlsonly) or (not hasWechat)
    return cb null, ret

  # 获取推荐用户信息
  if ret.items?.length and params.needRecommendUserInfo
    isSpUser = (not params.isRealtor) and user?.flwng
    ret.uid = flwngUid = user.flwng[0]?.uid if isSpUser
    try
      ret.ul = await getUsersInfo {req,isRealtor:params.isRealtor,isSpUser,flwngUid,list:ret.items}
    catch err
      debug.error err
      ret.ok = 0
      ret.ul = []

  return cb null, ret
DEF 'getPropList', getPropList

# APP '1.5'
# APP 'test', true
# POST '', (req,resp)->
#   resp.send 'ok'

APP '1.5'
APP 'city', true
#/1.5/prop/detail/inapp?id=TRBN11111
#/1.5/city/select
GET 'select',(req,resp)->
  # if req.getDevType() isnt 'app'
  #   return resp.ckup 'generalError',{err:"Error Loading Page"}
  return resp.ckup 'citySelectModal',{},"_",{noUserModal:1,wxcfg:{},noAppCss:1,noRatchetJs:1,noAngular:1}

VIEW 'citySelectModal',->
  # js '/js/ratchet/segmented-controllers.js'
  div id:'vueBody',->
    text """<app-city-select-modal></app-city-select-modal>"""
  # coffeejs {vars:{id:@id}},->
  #   null
  # js '/js/lz-string.min.js'
  # js '/js/calculate.min.js'
  js '/js/entry/commons.js'
  js '/js/entry/appCitySelectModal.js'


APP '1.5'
APP 'calc', true
POST 'exchange.json',(req,resp)->
  rates =
    exrates:getExchangeRates()
    exnames:getExchangeNames()
  for k, v of rates.exnames
    rates.exnames[k] = req.l10n v
  return resp.send rates

# TODO: future ver in specialties component
# APP '1.5'
# APP 'user', true
# specialties_list = [
#   'realtor','mortgage','immigration','law','finance','accountant','insurance'
# ]
# specialties_map = {
#   'realtor':[
#     {o:'For Sales',ctx:''}
#     {o:'Rentals',ctx:''}
#     {o:'Luxury Homes',ctx:''}
#     {o:'Offices', ctx:''}
#     {o:'Assignment'}
#     {o:'Rebuilding'}
#     {o:'Commercial'}
#     {o:'Condo'}
#   ]
# }
# specialties_addon = [
#   {o:'Mandarian',ctx:''},
#   {o:'Cantoenese'}
#   {o:'English'}
#   {o:'New Immigrant'}
#   {o:'International Student'}
#   {o:'Investor'}
# ]
# translateSpecialties = (req, l)->
#   ret = []
#   for i in l
#     tmp = {
#       o: i.o
#       n: if i.ctx then req.l10n(i.o, i.ctx) else req.l10n(i.o)
#     }
#     ret.push tmp
#   ret
# POST 'specialties.json',(req,resp)->
#   error = (err)->
#     resp.send {ok:0, e:req.l10n(err)}
#   tp = req.param tp
#   unless tp in specialties_list
#     return error('Not Supported')
#   ss = translateSpecialties(pecialties_map[tp] or [])
#   resp.send ss

#'/1.5/translate'
APP '1.5'
APP 'translate', true

# initTranslateCfg = ()->
#   SysData.findOne {_id:'translateConfig'},(err,ret)->
#     if err
#       debug.error err
#       return
#     if ret?.clear
#       translateConfig.clear = ret.clear
# initTranslateCfg()
#should user clear cache
shouldClear = (clmt)->
  # return true unless clmt
  # NOTE: for older versions, new clmt is Date.now()
  # debug.debug  '+++++++',clmt
  unless /\d+/.test clmt
    return true
  nowVersion = getConfig('vueCacheExpireDate')
  return true unless nowVersion
  parseInt(clmt) < parseInt(nowVersion)
  # (new Date(clmt) or new Date()).getDate() < now.getDate()
  # debug.debug  '++++'+getConfig('translateClear')
  # getConfig('translateClear') and isOldCache

LANGUAGE_LIST = DEF 'LANGUAGE_LIST'
# data passed as k:'', c:''
# store as "k:c" : v
POST (req, resp) ->
  error = (err) -> resp.send {ok:0, err:err}
  keys = req.param 'keys'
  abkeys = req.param 'abkeys'
  # return error "Bad Param"
  unless tlmt = req.param 'tlmt'
    tlmt = statHelper.getLastWeek(new Date())
  unless clmt = req.param 'clmt'
    # NOTE: translate version code
    clmt = getConfig('vueCacheExpireDate')
  req.setupL10n() unless req._ab and req._
  locale = req.body?.locale
  if req.param 'varsLang'
    locale = req.param 'varsLang'
  if (locale) and (locale in LANGUAGE_LIST)
    req.setLocale locale
  locale ?= req.locale()
  ret = {locale:locale, ok:1, keys:{}, abkeys:{}}
  # TODO: from session, clear every 7 days?
  if shouldClear(clmt)
    ret.clearCache = true
    clmt = getConfig('vueCacheExpireDate')
  if keys
    for k,v of keys
      if v.k
        if v.c # better not to use req.l10n('key', ''), '' as ctx
          ret.keys[k] = req.l10n(v.k, v.c) #or ret.keys[k]
          # TODO: support "{{tr}}",ctx use directive
          # ret.keys[k] = 'xxx'+v.k
          # ret.keys[k] = ret.keys[k].replace(/\[/g,'{')
          # ret.keys[k] = ret.keys[k].replace(/\]/g,'}')
        else
          ret.keys[k] = req.l10n(v.k) #or ret.keys[k]
          # ret.keys[k] = 'xxx'+v.k
        debug.error('Missising Translation!:'+v.k) unless ret.keys[k]

  #k:'epl', c:'rtp' -> _ 'Entire Place'
  if abkeys
    for k,v of abkeys
      if v.k
        if v.c
          ret.abkeys[k] = req._ab(v.k, v.c) #or ret.abkeys[k]
        else
          ret.abkeys[k] = req._ab(v.k) #or ret.abkeys[k]
        debug.error('Missising abTranslation!:'+v.k) unless ret.abkeys[k]
  # use tlmt(translate modified time) to get translated added later than tlmt
  i18n.filterByTimestamp new Date(tlmt),locale,(err,list)->
    if err
      debug.error err,'i18n.filterByTimestamp'
      return error(err)
    list ?= []
    for v in list
      # bug if translation not exists gives null
      ret.keys[v._id] = v[locale] or v.orig or (vv=(v._id.split(':'));vv.pop();vv.join(":"))
    ret.tlmt = new Date()
    ret.clmt = clmt
    return resp.send ret


APP '1.5'

convertRMListingsDispToTreb = propSearch.convertRMListingsDispToTreb
# get user info from prop list
getUsersInfo = ({req,isRealtor,isSpUser,flwngUid,list})->
  userList = []
  for i in list
    if (i.src in ['RM','RMTRB']) or (i.ltp in ['exlisting','assignment','rent'])
      # userDict[i.uid] = 1
      if i?.market?.cmstn?
        i.cmstn = i.market.cmstn
        # i.adok = true
      if i?.market?.cmstn and isSpUser #listing from agent and isSpUser
        userList.push flwngUid
        i.flwng = 1
      else #uploaded by user not agent, or not spUser
        userList.push i.uid
      unless isRealtor
        delete i.market
  if not userList.length
    return []
  ret = await UserModel.getListByIds userList,{projection:UserModel.BASIC_INFO_FIELDS}
  for u in ret
    if req.hasRole 'realtor', u
      u.realtor = 1
    if req.isAllowed 'vipRealtor', u
      u.vip = 1
    u.fnm = libUser.fullNameOrNickname(req.locale(),u)
    u.eml = if Array.isArray(u.eml) then u.eml[0] else u.eml
    u.avt = libPropertyImage.replaceRM2REImagePath u.avt if u.avt
    delete u.roles
  return ret

# find all listings in UserListings
# setUpPropFav=DEF 'setUpPropFav'
getRMProjects = (req, user, data)->
  limit = data.psize or 50
  if data.page and page = parseInt(data.page)
    data.skip = page * limit
  skip = data.skip or 0
  type = data.type or 'projects'
  ret = {
    ok:1
    type,
    skip:skip
    ts:req.param('ts')
  }
  isRealtor = req.hasRole 'realtor'
  isSpUser = (not isRealtor) and user?.flwng
  ret.uid = user.flwng[0]?.uid if isSpUser
  ltype = data.ltype or data.ltp or 'assignment'
  data.ltp = ltype
  lret = await Project.findRMProjects {data,req,user,limit,skip,ret}
  for p in lret
    p.isProj = true
    p.img.l = libPropertyImage.replaceRM2REImagePathForArray(p.img.l) if p?.img?.l
    libProject.setupUserFav {hasRoleRealtor:req.hasRole 'realtor'},user,p
    p.top = p.top?
    p.rcmd = p.rcmd?
    if Array.isArray p.favUsr
      p.favU = p.favUsr.length
      delete p.favUsr
    if Array.isArray p.favRealtor
      p.favR = p.favRealtor.length
      delete p.favRealtor
  isApp = req?.getDevType() is 'app'
  projs = await Project.injectProjSpUserAvtAndNm isApp,lret
  ret.items = projs
  return ret
# DEF 'getRMProjects',getRMProjects
APP 'props', true
# /1.5/props/delete
POST 'delete',(req,resp)->
  error = (err)->
    resp.send {ok:0, e:err}
  UserModel.appAuth {req,resp}, (user) ->
    unless req.isAllowed 'admin'
      return error('No auth')
    unless id = req.param 'id'
      return error('Need id')
    _id = req.param '_id'
    debug.debug  'MSG: admin delete rmprop: '+id
    Properties.removeUserListing {id,_id,isAdmin:true},(err,ret)->
      if err
        debug.error err,'Properties.removeUserListing'
        return error err
      resp.send {ok:1,msg:'Deleted'}

# /1.5/props/addSaveSearch
POST 'addSaveSearch', (req, resp)->
  q = req.body.q
  libP.filterSearchParams(q)
  readable = req.body.readable or getQueryReadable(req.body.q)
  error = (err)->
    resp.send {ok:0, e:err}
  unless q and readable
    return error('no requared data')
  UserModel.appAuth {req,resp}, (user) ->
    return error('Need login') unless user
    UserModel.addSavedSearch user._id, {q,readable}, (err,ret)->
      return error(err.toString()) if err
      resp.send {ok:1, msg:req.l10n('Saved')}

getUserOrLoginUser = (req,resp,cb)->
  lang = req.param 'lang'
  UserModel.appAuth {req,resp}, (user) ->
    return cb(user) if user
    return cb() if not (i = req.param 'i')
    {err,uid,ts} = UserModel.decodeUserFromParam i
    if err or (helpers.dayDiff(ts, new Date())>1) or (not uid)
      debug.error 'getUserOrLoginUser passcode expired'
      return cb()
    #检查用户是否存在
    UserModel.findById uid,{},(err,user)->
      if err
        debug.error err,'getUserOrLoginUser'
        return cb()
      return cb(user)

# /1.5/props/updateSavedSearch
# updateSavedSearch from native update btn
POST 'updateSavedSearch', (req, resp)->
  body = req.body
  error = (err)->
    resp.send {ok:0, e:err}
  UserModel.appAuth {req,resp}, (user) ->
    return error('Need login') unless user
    {ts,k,r,idx} = body
    UserModel.updateSavedSearchKandReadable user._id,{ts,k,r,idx},(err,ret)->
      return error(err.toString()) if err
      msg = req.l10n('Updated')
      return resp.send {ok:1, msg}

# /1.5/props/sbscbSaveSearch
# subscrivedSavedSearch
POST 'sbscbSaveSearch', (req, resp)->
  body = req.body
  error = (err)->
    resp.send {ok:0, e:err}
  getUserOrLoginUser req,resp,(user)->
    return error('Need login') unless user
    {ts,nm,sbscb,clnt,lang} = body
    # ts = new Date(req.body.ts) or new Date()
    # # q.id = 'fail remove flag'
    # update =
    #   $set:
    #     'savedSearch.$.nm':body.nm
    #     'savedSearch.$.sbscb':body.sbscb
    # update.$set['savedSearch.$.clnt']=body.clnt if body.clnt
    # update.$set['savedSearch.$.lang']=body.lang if body.lang
    # if body.clnt is null
    #   update.$unset={}
    #   update.$unset['savedSearch.$.clnt']=1
    #   update.$unset['savedSearch.$.lang']=1
    # return error('No Data') unless ts #or q
    try
      ret = await UserModel.updateSavedSearch user._id,{ts,nm,sbscb,clnt,lang}
    catch err
      return error(err.toString())
    if ret?.isDuplicatedName
      msg = req.l10n('Duplicated name. Renamed to ') + ret.nm
    else
      msg = req.l10n(if sbscb then 'Watched' else 'Unwatch')
    # 返回nm用于前端列表展示
    return resp.send {ok:1, msg, isDuplicatedName:ret?.isDuplicatedName,nm:ret?.nm}
# TODO: add translation
# l = readlable list = [{k:'', v:''}]
# translateSavedSearch = (req, l)->
#   ret = ''
#   for i in l
#     ret += ', ' if ret
#     ret += (i.vv  or '')+ (if i.v then ' '+i.v else '')
#   ret

# /1.5/props/checkSbscb
# check subscribed SavedSearch quantity
POST 'checkSbscb', (req, resp)->
  getUserOrLoginUser req,resp,(user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,errorCode:0 ,resp} unless user
    isVip = req.isAllowed 'vipUser'
    isRealtor = req.hasRole 'realtor'
    try
      canAdd = await UserModel.checkSbscbByRole {uid:user._id,isVip}
    catch err
      console.error err
      return respError {category:MSG_STRINGS.DB_ERROR,errorCode:0 ,resp}
    ret = {ok:1}
    if canAdd is false
      msg = MSG_STRINGS.OVERSBSCB
      if isRealtor
        msg = MSG_STRINGS.OVERSBSCB_REALTOR
    ret.msg = req.l10n msg
    return resp.send ret

# NOTE: saleDesc required for new search conditions, if none will cause disp bug in native
getSaleDescFromQ = (q={})->
  if /rent|lease/ig.test q.saletp
    if /-/.test(q.dom) or q.status is 'U'
      return 'Leased'
    else
      return 'Rent'
  else
    if /-/.test(q.dom) or q.status is 'U'
      return 'Sold'
    else
      return 'Sale'

prepareSavedSearch = (req, l)->
  req.setupL10n() unless req._ab
  l ?= []
  # {k:queryObj, r:readable, v:text}
  isApp = req?.getDevType() is 'app'
  for search in l
    # search.v = translateSavedSearch(req, search.r)
    search.r ?= []
    search.k ?= {}
    # NOTE: *
    if not (search.k.saleDesc)
      search.k.saleDesc = getSaleDescFromQ(search.k)
    # *if soldOnly not exists
    # NOTE: this value is requird for new search conditions even if sale/rent(sold/leased) will ignore if sale
    if not(search.k.soldOnly?)
      search.k.soldOnly = true
    if search.k.bnds
      bndsAndBbox = bnds:search.k.bnds
      width = 200
      height = 200
      {mapUrl} = buildMapboxMapUrl bndsAndBbox,{
        isApp,
        hasBoundary:1,padding:5,width,height
      }
      search.mapBndsImg = mapUrl
    for r in search.r
      # r.vv = ''+r.vv if r.vv
      # console.log '+++',r
      if r.k is 'bsmt'
        r.vv = req._ab r.v, 'bsmt'
      else if r.k in ['bbox','price','cmty']
        # r.vv = r.vv
      else if r.vv
        if Array.isArray r.vv
          tmp = []
          for i in r.vv
            tmp.push req.l10n(i)
          r.vv = tmp
        else
          # console.log '++',r.vv,req.l10n(r.vv)
          r.vv = req.l10n(r.vv)
      r.kv = req.l10n(r.kv, r.k) if r.kv

dataMethods.savedSearchCount = (req, user, cb)->
  # return cb null, 0
  UserModel.savedSearchCount {user}, (err, ret)->
    return cb(err.toString()) if err
    cb null,ret

# /1.5/props/savedSearch
POST 'savedSearch', (req, resp)->
  error = (err)->
    resp.send {ok:0, e:err}
  return error('no required data') unless mode = req.body.mode
  getUserOrLoginUser req,resp,(user)->
    return error('Need login') unless user
    if mode is 'get'
      UserModel.findProfileById user._id,{projection:{savedSearch:1}}, (err,ret)->
        return error(err.toString()) if err
        return resp.send {ok:1} unless ret?.savedSearch
        prepareSavedSearch(req, ret?.savedSearch)
        try
          savedSearch = await CrmModel.getClient ret?.savedSearch
        catch err
          return error(err.toString())
        savedSearch ?= []
        l10n = (a,b)-> req.l10n a,b
        for i in savedSearch
          i.display = renderSavedSearchTxt({savedSearch:i,l10n})
        return resp.send {ok:1, l:savedSearch}
    else #mode is 'del'
      # q = req.body.q
      ts = new Date(req.body.ts) or new Date()
      idx = req.body.idx
      # q.id = 'fail remove flag'
      return error('No Data') unless ts #or q
      UserModel.deleteSavedSearch user._id, {ts},(err,ret)->
        return error(err.toString()) if err
        # if ret.result?.nModified is 1
        return resp.send {ok:1, msg:req.l10n('Removed')}
        # else
        #   return error(req.l10n('Data not found'))

POST 'recentFavProps', (req, resp)->
  UserModel.appAuth {req,resp}, (user) ->
    return respError {category:MSG_STRINGS.NEED_LOGIN,errorCode:0 ,resp} unless user
    isAllowedPropAdmin = UserModel.accessAllowed('propAdmin',user)
    isAllowedVipUser = UserModel.accessAllowed('vipUser',user)
    req.setupL10n() unless req._ab
    Properties.findRecentFavProps {user,limit:10,isAllowedVipUser,isAllowedPropAdmin},\
    (err,ret)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp} if err
      ret = propTranslate.translate_rmprop_list(req, ret)
      libP.thumbUrlListReplace(ret, req.isChinaIP(), config.share?.hostNameCn)
      try
        ret = await Properties.addPropFavFields {props:ret,uid:user?._id}
        isRealGroup = await GroupModel.isInGroup {uid:user?._id,groupName:':inReal'}
      catch err
        debug.error 'recentFavProps',err
      for p in ret
        Properties.addPropAdditionalFields({
          user,prop:p,
          isAllowedPropAdmin,
          isAllowedVipUser,
          l10n:(a,b)->req.l10n(a,b),
          _ab:(a,b)->req._ab(a,b),
          apisrc:req.param('apisrc')
          isRealGroup,
          simplfyPtp:false})
        setPropWebUrl(req,p)
        Properties.deletePropPriviteFields({user,prop:p})
      resp.send {ok:1, items:ret}

# TODO: new file propFav.coffee, move below to new file

#find user fav props by grp
# /1.5/props/favProps
POST 'favProps', (req, resp)->
  {grp,lang,limit=20,favSort} = req.body
  # error = (err, ec)-> #TODO: use respError
  #   resp.send {ok:0, e:err, ec:ec}
  UserModel.appAuth {req,resp}, (user) ->
    # return error('Need login', 0) unless user
    # return respError {clientMsg:'Need login',errorCode:0 ,resp} unless user
    return respError {category:MSG_STRINGS.NEED_LOGIN,errorCode:0 ,resp} unless user
    page = req.param 'page'
    grp = parseInt(grp) or 0
    UserModel.findProfileById user._id,{}, (err, prof)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp} if err
      grps = prof?.favMap or {'0':{v:'Default'}, cntr:1}
      isAllowedPropAdmin = UserModel.accessAllowed('propAdmin',user)
      isAllowedVipUser = UserModel.accessAllowed('vipUser',user)
      req.setLocale(lang) if lang
      req.setupL10n() unless req._ab
      try
        {ret,count} = await Properties.findUserFavProps {user,page,limit,grp,grps,isAllowedVipUser,isAllowedPropAdmin,favSort}
      catch err
        debug.error err
        return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp}
      ret = propTranslate.translate_rmprop_list(req, ret)
      libP.thumbUrlListReplace(ret, req.isChinaIP(), config.share?.hostNameCn)
      try
        ret = await Properties.addPropFavFields {props:ret,uid:user?._id}
        isRealGroup = await GroupModel.isInGroup {uid:user?._id,groupName:':inReal'}
      catch err
        debug.error 'findProfileById',err
      for p in ret
        Properties.addPropAdditionalFields({
          user,prop:p,
          isAllowedPropAdmin,
          isAllowedVipUser,
          l10n:(a,b)->req.l10n(a,b),
          _ab:(a,b)->req._ab(a,b),
          apisrc:req.param('apisrc')
          isRealGroup,
          simplfyPtp:false})
        setPropWebUrl(req,p) #TODO: dup with #180
        Properties.deletePropPriviteFields({user,prop:p})
      # libP.genListingThumbUrlForList(ret, isCip:req.isChinaIP(), isPad:req.isPad(), config.use3rdPic)
      totalPages = Math.ceil(count / limit)
      isNoteAdmin = req.isAllowed 'noteAdmin'
      uaddrs = []
      countInfo = []
      for item in ret
        uaddrs.push(item.uaddr) unless uaddrs.includes item.uaddr
      try
        if isNoteAdmin or isRealGroup
          {noteList} = await InRealNoteModel.getNoteList {mode:2,uid:user._id,uaddrs}
          countInfo = await InRealNoteModel.getNoteCountInSaves uaddrs
        else
          {noteList} = await PersonalNoteModel.getNoteList {mode:2,uid:user._id,uaddrs}
      catch err
        debug.error err
        return respError {category:MSG_STRINGS.DB_ERROR, resp}
      if isNoteAdmin or isRealGroup
        ret = mergeMemoByMode {mode:'inReal',ret,noteList,countInfo}
      else
        ret = mergeMemoByMode {mode:'personal',ret,noteList}
      try
        group = await CrmModel.getClient [grps[grp]]
        archivedGrps = await PropFavouriteModel.findArchivedFolder user?._id
      catch err
        debug.error err
        return respError {category:MSG_STRINGS.DB_ERROR, resp}
      grps[grp] = group[0] if group
      resp.send {ok:1, items:ret, grps: grps, cnt:count, totalPages:totalPages,archivedGrps}

initTopListings =  DEF 'initTopListings'

#add fav to prop
# /1.5/props/favProp
POST 'favProp', (req, resp)->
  _id = req.body.id
  grp = parseInt(req.body.grp)
  mode = req.body.mode
  topTs = req.body.topTs
  src = req.body.src
  isTopListing = new Date(topTs) > new Date()
  error = (err, ec)->
    resp.send {ok:0, e:err, ec:ec}
  unless _id and not isNaN(grp)
    return error('no requared data')
  UserModel.appAuth {req,resp}, (user) ->
    return error('Need login', 0) unless user
    Properties.addUserFavProp {id:_id,user,grp,mode},(err,ret)->
      if err
        debug.error err,'appAuth addUserFavProp'
        return error err
      if isTopListing
        initTopListings({src:src})
      resp.send {ok:1, msg:req.l10n(ret,'favorite')}
      if mode is 'favour'
        UserModel.updateFavmt {uid:user._id,grp}

# /1.5/props/viewedProps
POST 'viewedProps', (req, resp)->
  error = (err, ec)->
    resp.send {ok:0, e:err, ec:ec}
  UserModel.appAuth {req,resp}, (user) ->
    return respError {
      clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),
      category:MSG_STRINGS.NEED_LOGIN,
      resp} unless user
    page = req.body?.page or 0
    limit = req.body?.limit or 20
    UserModel.getViewedHistories {id:user._id,page,limit}, (err, logs)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp} if err
      return resp.send {ok:1, items:[]} unless logs?.length > 0
      req.setupL10n() unless req._ab
      opt = libP.getBaseForPropList req,user
      try
        opt.isRealGroup = await GroupModel.isInGroup {uid:user._id,groupName:':inReal'}
      catch err
        debug.error err
        return respError {category:MSG_STRINGS.DB_ERROR, resp}
      # libP.genSearchMergeOrDelProps opt
      Properties.findUserViewedProps {logs,searchMergeProps:opt.searchMergeProps,searchDelProps:opt.searchDelProps},(err,ret)->
        return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp} if err
        return resp.send {ok:1, items:[]} unless ret?.length > 0
        ret = propTranslate.translate_rmprop_list(req, ret)
        libP.thumbUrlListReplace(ret, req.isChinaIP(), config.share?.hostNameCn)
        try
          ret = await Properties.addPropFavFields {props:ret,uid:user?._id}
          isRealGroup = await GroupModel.isInGroup {uid:user?._id,groupName:':inReal'}
        catch err
          debug.error 'findUserViewedProps',err
        for p in ret
          Properties.addPropAdditionalFields({
            user,prop:p,
            isAllowedPropAdmin: UserModel.accessAllowed('propAdmin',user),
            isAllowedVipUser: UserModel.accessAllowed('vipUser',user),
            l10n:(a,b)->req.l10n(a,b),
            _ab:(a,b)->req._ab(a,b),
            apisrc:req.param('apisrc')
            isRealGroup,
            simplfyPtp:false})
          setPropWebUrl(req,p) #TODO: dup with #180
          Properties.deletePropPriviteFields({user,prop:p})
        # libP.genListingThumbUrlForList(ret, isCip:req.isChinaIP(), isPad:req.isPad(), config.use3rdPic)
        resp.send {ok:1, items:ret}

dataMethods.userFavGroups = (req, user, cb)->
  map = {'0':{v:'Default'}, cntr:1}
  unless user
    return cb null, null
  UserModel.findProfileById user._id,{}, (err, ret)->
    return cb(err.toString()) if err
    favMap = ret?.favMap or map
    cb null,favMap

dataMethods.allowedEditGrpName = (req, user)->
  return req.isAllowed('editGrpName')

# dataMethods.showSoldPrice = (req, user)->
#   return getConfig('showSoldPrice')

# dataMethods.showIndexReno = (req, user)->
#   return getConfig('showIndexReno')

dataMethods.showSoldPriceBtn = (req, user)->
  return true
  # return getConfig('showSoldPriceBtn')

dataMethods.isPaytopAll = (req, user)->
  return getConfig('isPaytopAll')

dataMethods.rltrTopAd = (req, user)->
  return getConfig('rltrTopAd')

dataMethods.brokerRmrkToVip = (req, user)->
  return getConfig('brokerRmrkToVip')

# grp management
# /1.5/props/propGroups
POST 'propGroups', (req, resp)->
  error = (err,redirect_uri='')->
    resp.send {ok:0, e:err,redirect_uri}
  UserModel.appAuth {req,resp}, (user) ->
    return error('Need login','/1.5/user/login') unless user
    mode = req.body.mode
    if mode is 'set' #push to list, create new
      # favMap: [{k:'star',v:'Fav'},{v:''},{v:''}]
      # TODO: use user model
      nm = req.body.nm
      clnt = req.body.clnt
      cNm = req.body.cNm
      return error(req.l10n 'Not a valid group name') unless nm
      MAX_GROUPS = 6 #includes a cntr
      MAX_GROUPS = 50 if req.isAllowed 'vipRealtor'
      UserModel.setFavoriteGroup user._id,{nm,MAX_GROUPS,clnt,cNm},(err, ret)->
        if err
          debug.error err,'setFavoriteGroup'
          return error(err)
        grps = ret.value?.favMap
        resp.send {ok:1, grps:grps}
    else if mode is 'delete' # remove groupe by index
      grp = parseInt(req.body.grp)
      if isNaN(grp)
        return error(req.l10n 'Not a valid group name')
      if grp is 0
        return error(req.l10n("Cannot remove default group"))
      Properties.clearUserFavsByGroup {user,grp},(err,msg)->
        if err
          debug.error err,'clearUserFavsByGroup'
          return error(err)
        UserModel.deleteFavoriteGroup {id:user._id,nm:grp}, (err, ret)->
          if err
            debug.error err,'deleteFavoriteGroup'
            return error(err)
          grps = ret.value?.favMap
          resp.send {ok:1, grps:grps}
    else if mode is 'put' # change v by index
      newV = req.body.nm
      clnt = req.body.clnt
      cNm = req.body.cNm
      grp = parseInt(req.body.grp)
      if (not newV) or isNaN(grp)
        return error(req.l10n 'Not a valid group name')
      UserModel.putFavoriteGroup {id:user._id,nm:grp,newV,clnt,cNm},(err, ret)->
        if err
          debug.error err,'putFavoriteGroup'
          return error(err)
        resp.send {ok:1, grps:ret.value?.favMap}
    else if mode is 'clear' # clear all fav by group idx
      grp = parseInt(req.body.grp)
      if isNaN(grp)
        return error(req.l10n 'Not a valid group name')
      Properties.clearUserFavsByGroup {user,grp},(err,msg)->
        if err
          debug.error err,'clearUserFavsByGroup'
          return error(err)
        resp.send {ok:1, msg:req.l10n(msg)}
      return
    else #get all list
      # favMap: [{k:'star',v:'Fav'},{v:''},{v:''}]
      UserModel.findFavoriteMap user._id, (err, ret)->
        return error(err.toString()) if err
        favMap = ret?.favMap or {'0':{v:'Default'}, cntr:1}
        resp.send {ok:1, grps:favMap}


# l10nCityList = {} # will not update if updated
# /1.5/props/cities.json
POST 'cities.json', (req, resp)->
  ret = {ok:1}
  if req.param('apisrc') is 'miniapp.sold'
    ret.fc = ProvAndCity.getMinappPopularCities()
  else if p = req.param 'p'
    param = {provName:p, needLoc:req.param('loc')}
    ret.fc = ProvAndCity.getTranslatedPopularCityList(req.locale(),HOT_CITIES_LIMIT,p)
    ret.cl = ProvAndCity.getCitiesByProv(req.locale(), param)
  else
    ret.fc = ProvAndCity.getTranslatedPopularCityList(req.locale())
  resp.send ret

getCmtyByProvAndCityStats = (req, param, cb)->
  propStatsModel.findCmtyByProvCity param, (err,result)->
    return cb err,[] if err
    result ?= []
    return cb null, [] unless result?.length
    lastRecord = result[result.length-1]
    return cb null, [] unless (Object.keys(lastRecord.cmty) or []).length
    ret = []
    for k,v of lastRecord.cmty
      ret.push {k:v.nm, v:v.nm, onm:v.onm}
    cb null, ret

# /1.5/props/cmty.json
POST 'cmty.json', (req, resp)->
  ret = {ok:1}
  unless (p = req.param 'p') and (cityName = req.param 'city')
    return resp.send {ok:0, err:req.l10n('Need both prov and city!')}
  param = {provName:p, cityName:cityName}
  if req.param('stat')
    getCmtyByProvAndCityStats req, param, (err, cl)->
      if err
        debug.error err,'getCmtyByProvAndCityStats'
        req.logger?.error err
      ret.cl = cl
      resp.send ret
  else
    ret.cl = ProvAndCity.getCmtyByProvAndCity(req.locale(), param)
    resp.send ret

# /1.5/props/provs.json
POST 'provs.json', (req, resp)->
  ret = ProvAndCity.getAllTranslatedProvs(req.locale())
  if supportedProvs = req.setting.supportedProvs
    ret = ret.filter (prov)->
      return prov.o_ab in supportedProvs
  resp.send {ok:1, p:ret}

# /1.5/props/ptype2s.json
POST 'ptype2s.json', (req, resp)->
  {ptype2s,ptype2sShort} = Properties.getAllTranslatedPtype2s({lang:req.locale(),ptype:req.body?.ptype})
  resp.send {ok:1, ptype2s, ptype2sShort}

# NOTE: [mapSearch] return json
# /1.5/props/search
# @description 搜索房源接口
POST 'search', (req, resp)->
  PropSearchLimiterFilter req,resp,()->
    # 错误处理函数
    error = (err)->
      resp.send {ok:0, err:err}

    # 用户认证
    UserModel.appAuth {req,resp}, (user) ->
      params = req.body or {}

      # MLS住宅类型特殊处理
      if (params.src is 'mls') and (params.ptype is 'Residential')
        params.rmProp = true

      # 获取房源列表
      getPropList req, resp, user, params, (err, ret)->
        return error(err) if err
        # if params.src is 'rm'
        #   return resp.send ret
        ret.ok = 1
        ret.ts = req.param('ts')
        if ret.query?.param
          ret.q = ret.query.param
        if ret.query?.readable
          ret.readable = ret.query.readable
        delete ret.query
        # 如果设置了skipFav参数,只返回基本数据
        if params.skipFav
          # 只保留必要的字段
          ret = {
            ok: ret.ok,
            items: ret.items,
            cnt: ret.cnt
          }
        resp.send ret


# get_ad_user_from_prop = DEF 'get_ad_user_from_prop'
# getPropCityCmtyStats = DEF 'getPropCityCmtyStats'
# setLang = DEF 'setLang'
# /1.5/props/vipinfo

POST 'vipinfo', (req, resp)->
  error = (err)->
    resp.send {ok:0, err:req.l10n(err)}
  UserModel.appAuth {req,resp}, (user) ->
    return error('VIP Only') unless req.isAllowed 'vipRealtor', user
    # TODO: check if user is treb user?
    return error('Need Id') unless _id = req.param 'id'
    Properties.findOneByID _id, {projection:{uaddr:1,comm:1, bm:1, owner:1, lst:1, coop:1}}, (err, ret)->
      if err
        debug.error err
        return error(MSG_STRINGS.DB_ERROR)
      ret ?= {}
      ret.ok = 1
      ret.onm = ret.owner
      if ret.coop?.agnt and ret.coop.nm
        ret.coopagnt = (agnt.nm for agnt in ret.coop.agnt).join(' & ')
        ret.coopcpny = ret.coop.nm
      delete ret.coop
      delete ret.owner
      uaddr = ret.uaddr
      Properties.getRmStoreyInfo uaddr, (err,rmStoreyInfo)->
        if err
          debug.error err
          return error(MSG_STRINGS.DB_ERROR)
        #rmStoreyInfo: {rmStorey, missingLvls}
        if rmStoreyInfo
          for info in ['missingLvls']
            ret[info] = rmStoreyInfo[info] if rmStoreyInfo[info]
        return resp.send ret

# /1.5/props/lstdhist
POST 'lstdhist', (req, resp)->
  error = (err)->
    resp.send {e:err, ok:0}
  UserModel.appAuth {req,resp}, (user) ->
    # return error('no auth') unless user#req.isAllowed 'vipRealtor'
    addr = req.param 'addr'
    prov = req.param 'prov'
    city = req.param 'city'
    unt  = req.param 'unt'
    _id  = req.param '_id'
    lat  = req.param 'lat'
    lng  = req.param 'lng'
    # faddr = req.param 'faddr'
    uaddr = req.param 'uaddr'
    type = req.param 'type'
    zip = req.param 'zip'
    lng = helpers.formatLng lng
    st_num = ''+(req.param('st_num') or '')
    return error('no addr') unless uaddr #or (lat and lng))#addr and city and prov
    # faddr
    Properties.findAddrHistory {zip,prov,city,addr,unt,id:_id,lat,lng,uaddr,type},(err,ret)->
      if err
        debug.error err,'findAddrHistory'
        return error(err)
      if ret.msg?
        ret.msg = req.l10n ret.msg
      retList = []
      opt =
        isAllowedPropAdmin:req.isAllowed('propAdmin',user)
        isAllowedVipUser:req.isAllowed('vipUser',user)
        # configShowSoldPrice:getConfig('showSoldPrice')
        apisrc:req.param('apisrc')
      if ret?.l?.length
        for p in ret.l
          showValid = libP.filterPropSpLstField(opt,p)
          p.st_num = ''+(p.st_num or '')
          if showValid and (p.st_num is st_num)
            {saleTpTag, tagColor} = libP.getSaleTpTagAndTagColorAndLstStr(p,true)
            if saleTpTag
              p.saleTpTag = req.l10n(saleTpTag)
            p.tagColor = tagColor if tagColor
            retList.push p
        ret.l = retList
      return resp.send ret

# /1.5/props/trbdetail
POST 'trbdetail', (req, resp)->
  error = (err)->
    resp.send {e:err, ok:0}
  UserModel.appAuth {req,resp}, (user)->
    return error('Need login') unless user
    return error('Not auth') unless req.isAllowed 'propAdmin'
    return error('Need _id') unless _id = req.param '_id'
    Properties.findTrebRecordByID {id:_id},(err,prop)->
      if err
        debug.error err,'Properties.findTrebRecordByID'
        return resp.send {ok:0,err:err}
      resp.send {ok:1,prop:prop}

# 获取房源详情页的额外配置信息
# @param {Object} req - 请求对象,包含用户配置信息
# @param {Object} user - 当前用户对象
# @return {Object} opt - 配置选项对象
#   @prop {Boolean} transDDF - 是否需要翻译DDF数据
#   @prop {Boolean} isRealGroup - 用户是否属于RealGroup组
#   @prop {Boolean} brokerRmrkToVip - 经纪人备注是否对VIP可见
#   @prop {Boolean} isVipPlus - 是否是VIP Plus用户
getAdditionalConfig = (req,user) ->
  # 获取基础配置
  opt = libP.getBasePropDetailOpt req,user
  # 获取是否需要翻译DDF数据的配置
  opt.transDDF = req.getConfig('transDDF')
  # 检查用户是否属于RealGroup组
  try
    isRealGroup = await GroupModel.isInGroup {uid:user?._id,groupName:':inReal'}
  catch err
    debug.error 'GroupModel.isInGroup',err
  opt.isRealGroup = isRealGroup or false
  # 获取经纪人备注对VIP可见的配置
  opt.brokerRmrkToVip = req.getConfig('brokerRmrkToVip')
  # 检查是否是VIP Plus用户
  opt.isVipPlus = req.isAllowed('vipPlus')
  return opt

# /1.5/props/detail
# NOTE: send detail as json
POST 'detail', (req, resp)->
  error = (err, ec, url)->
    resp.send {e:err, ec:ec, url:url}
  UserModel.appAuth {req,resp},(user)->
    unless id = (req.param('id') or req.param('_id'))
      return error(MSG_STRINGS.BAD_PARAMETER,2,'/1.5/prop/error?id='+id)
    #Todo, check order
    opt = await getAdditionalConfig(req,user)
    isRealGroup = opt.isRealGroup
    excludes = Properties.genExcludes opt
    # TODO: remove reduncdent try catch
    try
      result = await Properties.getTranslatedPropDetailByID {
        id:opt.id,
        fromStr:req.getDevType(),
        trans:req.locale(), #req.param local or lang?
        disKeyOnly:true,
        additionalConfig:opt,
        projection:excludes,
        user
      }
    catch err
      debug.error 'Error: getTranslatedPropDetailByID',err
      return error(MSG_STRINGS.DB_ERROR,2,'/1.5/prop/error?id='+opt.id)
    if not result.detail
      return error(req.l10n(MSG_STRINGS.NOT_FOUND),2,'/1.5/prop/error?id='+opt.id)
    if result.detail?.needLogin
      msg = 'According to Real Estate Board rules, you have to login to see this listing!'
      return resp.send
        ok:0
        id:result.detail.id
        cnt:0
        e: req.l10n(msg)
        ec:1
    if (((req.isAllowed 'claimAdmin') or isRealGroup) and (not libP.isRMListing(result.detail))\
        and result.detail.lst)
      result.detail.claimed = false
      try
        claim = await ClaimModel.getById {uid:user._id,pid:result.detail._id}
        if claim?[0]?._id
          result.detail.claimed = true
        q = {uid:user._id,pid:result.detail._id,lst:result.detail.lst,sldd:result.detail.sldd,saletp:result.detail.saletp}
        claimInfo = await ClaimModel.checkIfCanClaim q
      catch err
        debug.error 'Error: getTranslatedPropDetailByID',err
      result.detail.canClaim = claimInfo?.canClaim
    # 获取估价租金数据
    if libP.isMlsPrefix(result.detail._id) and propTranslate.saletpIsSale(result.detail.saletp_en or result.detail.saletp)
      try
        estimateData = await EstimatedModel.getAllEstimateInfo(result.detail._id, {rent_mn1: 1})
      catch err
        debug.error 'Error: getAllEstimateInfo',err,' propId:',result.detail._id
      if estimateData?.rent_mn1
        result.detail.estRent = Number(estimateData.rent_mn1)
    resp.send result
      # getPropDetails opt, (err, prop)->
      #   if err and err.code is 1
      #     return resp.send
      #       ok:0
      #       id:err.id
      #       cnt:0
      #       e: req.l10n(err.msg)
      #       ec:1
      #   Properties.deletePropPriviteFields({user,prop})
      #   return error(req.l10n(err.msg), err.code, err.url) if err
      #   resp.send {ok:1, prop:prop}

# /1.5/props/edm
POST 'edm',(req, resp)->
  error = (err) -> resp.send {ok:0, err:err}
  UserModel.appAuth {req,resp}, (user)->
    return error(resp,'Need login') unless user
    return error(resp,'Not auth') unless req.isAllowed 'admin'
    _id = req.param '_id'
    return error(resp,'Need _id') unless _id
    Properties.updateRMpropEDM {id:_id, del:req.body.del}, (err,ret)->
      return error(resp,err.toString()) if err
      return resp.send {ok:1, edm:ret}

# /1.5/props/cmtyBnds
POST 'cmtyBnds', (req, resp)->
  _id = req.param '_id'
  if not _id
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp}
  try
    cmtyBnds = await Properties.getCmtyBnds _id
  catch err
    debug.error 'Properties.getCmtyBnds',err
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp}
  resp.send {ok:1, cmtyBnds}

# /1.5/props/cmtyImage
POST 'cmtyImage', (req, resp)->
  body = req.body
  if not ((body.cnty and body.prov and body.city and body.cmty) or body.id)
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp}
  Properties.getPropCmtyImage body, (err, cmty_image)->
    if err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp}
    resp.send {ok:1, cmty_image}

# /1.5/props/rltrs
POST 'rltrs', (req, resp)->
  body = req.body
  error = (err) -> resp.send {ok:0, err:err}
  if not (body.rltr)
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp}
  if (body.rltr+'').length < 2
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp}
  UserModel.appAuth {req,resp}, (user)->
    # return error(resp,MSG_STRINGS.NEED_LOGIN) unless user
    # return error(resp,MSG_STRINGS.ACCESS_DENIED) unless req.isAllowed 'vipRealtor'
    Properties.getRltrsFromProperties body.rltr, (err, rltrsList)->
      if err
        return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp}
      resp.send {ok:1, data:rltrsList}

###
# @description 将uaddr/uaddr+unt的笔记总数和个人笔记增加到saves里，用于前端显示
# @param {string} mode - 是非房大师用户还是房大师用户标记
# @param {array} ret - 用户收藏的房源
# @param {array} noteList - 笔记列表
# @param {array} countInfo - 房大师经纪针对uaddr写的笔记个数
# @return {array} ret - 增加了笔记信息的用户收藏房源
###
mergeMemoByMode = ({mode,ret,noteList,countInfo})->
  uaddrMap = {}
  noteCountMap = {}
  for list in noteList
    key = list.uaddr
    if mode is 'personal'
      key = "#{list.uaddr}+#{list.unt}"
    uaddrMap[key] = list
  if countInfo
    for count in countInfo
      if uaddrMap[count._id]
        uaddrMap[count._id].noteCount = count.count
      noteCountMap[count._id] = count.count
  for info in ret
    if /^RM/.test info.id # 只有MLS房源有笔记
      continue
    key = info.uaddr
    if mode is 'personal'
      key = "#{info.uaddr}+#{info.unt}"
    info.noteCount = 0
    if uaddrMap[key]
      info.noteId = uaddrMap[key]._id
      info.noteMemo = uaddrMap[key].memo or [{tag: 'noTag', m: ' '}]
      info.noteCount = if mode is 'personal' then 1 else uaddrMap[key].noteCount
    if (mode is 'inReal') and (not uaddrMap[key]) and noteCountMap[key]
      info.noteCount = noteCountMap[key]
  return ret

APP 'prop'
# /prop/picUrls 获取mls和rm房源的房源图片
# TODO1：检查referrer
# TODO2：未来会校验传进来的第一张图片path和获取的第一张图片path是否一致，确保请求是从房源详细来的（防止直接从房源id获得图片列表）
POST 'picUrls', (req, resp)->
  PropPicLimiterFilter req,resp,()->
    id = req.body?.id
    shortUrlId = req.body?.shortUrlId
    unless id
      return respError {category:MSG_STRINGS.BAD_PARAMETER, resp}
    UserModel.appAuth {req,resp}, (user)->
      isRmProp = false
      picUrls = []
      additionalConfig = await getAdditionalConfig(req,user)
      if libP.isPropObjectId(id) or libP.isRMListing({id})
        try
          prop = await Properties.findRMOneByID id,{pic:1,status:1,id:1}
        catch err
          debug.error err
          return respError {category:MSG_STRINGS.DB_ERROR, resp}
        isRmProp = true
      else
        projection =
          phoIDs:1
          src:1
          phoUrls:1
          sid:1
          photonumbers:1
          pho:1
          ts:1
          orgId:1
          useTrebPhoto:1
          phosrc:1
          ddfID: 1
          picUrl:1
          phoDl:1
          maxPicSz:1
          onD:1
          mt:1
          del:1
          status:1
          trbtp: 1
          topTs:1
          BrokerReciprocity:1
          phoLH:1
          phoP:1
          ListingKey:1  # use for DDF pic
        try
          prop = await Properties.findOneByIDAsync id, projection
        catch err
          debug.error err
          return respError {category:MSG_STRINGS.DB_ERROR, resp}
      unless prop
        return respError {category:MSG_STRINGS.NOT_FOUND, resp}
      # admin,inReal,vip以外用户不能查看隐藏房源详情
      if prop.del and (not libP.canViewDelProp additionalConfig)
        return respError {category:MSG_STRINGS.NOT_FOUND, resp}
      if isRmProp # rm房源不需要登录也可以查看
        if imgUrls = prop.pic?.l
          prop.picUrls = libPropertyImage.getRMListingsImages {sid:prop.sid,imgUrls}
        else
          prop.picUrls = []
      else
        isShare = additionalConfig.isShare or additionalConfig.fubSrc or additionalConfig.isEdm
        # mls房源和房源详情的是否登录才能查看保持一致
        if shortUrlId and shortUrlId.length
          try
            doc = await ShortUrl.getById shortUrlId
          catch err
            debug.error err
            return respError {category:MSG_STRINGS.DB_ERROR, resp}
          if doc?.type isnt 'shareProp'
            return respError {category:MSG_STRINGS.NEED_LOGIN,clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),resp}
        else
          loginRet = setNeedLogin {prop,user,isShare,isSearchEngine:additionalConfig.isSearchEngine}
          if loginRet?.needLogin
            return respError {category:MSG_STRINGS.NEED_LOGIN,clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),resp}
        libP.genListingPicReplaceUrls(prop, additionalConfig.isChinaIP, {
          use3rdPic:config.serverBase?.use3rdPic,
          isPad:additionalConfig.isPad,
          useThumb:additionalConfig.useThumb,
          shareHostNameCn:config.share?.hostNameCn,
          isLarge:additionalConfig.isLarge
        })
      picUrls = listingPicUrlReplace(prop)
      return resp.send {ok:1,picUrls}