should = require('should')
testHelpers = require('../00_common/helpers')
helpersDate = require('../../built/lib/helpers_date')

# ./unitTest/test.sh -f lib/helpers_date.js
describe 'Helpers function tests',->
  before (done) ->
    @timeout(300000)
    done()
  
  describe 'inputToDate',->
    tests=[
      {
        input:'Mon, 18 Dec 1995 17:28:35 GMT',
        output:new Date('Mon, 18 Dec 1995 17:28:35 GMT'),
      },
      {
        input:'2013W065'
        output: new Date('2013/02/08'),
      },
      {
        input:'20130208T080910'
        output: new Date('2013/02/08 08:09:10'),
      }
      {
        input:'2013-02-08 09:30:26.123-07:00'
        tz:'America/Phoenix'
        output: new Date('2013-02-08 09:30:26-07:00'),
      },
      {
        input:'9/28/2021'
        output: new Date('2021-09-28 04:00:00.000Z'),
      }
      {
        input:'2020-10-20 03:44:20.000Z',
        output: new Date('2020-10-20 03:44:20.000Z'),
      },
      {
        input:'2021-04-09 13:47:04.0'
        output: new Date('2021/04/09 13:47:04.0'),
      },
      {
        input:'2020-10-19 00:00:00.0'
        output: new Date('2020/10/19 00:00:00'),
      }
      {
        input:'/Date(1620273600000-0400)/',
        output: new Date('2021-05-06T04:00:00.00Z'),
      },
      {
        input:20190122,
        output: new Date('2019/01/22'),
      },
      {
        input: 1264118400000,
        output: new Date('2010-01-22T00:00:00.000Z'),
      },
      {
        input:'2011-01-22'
        output: new Date('2011/01/22'),
      }
    ]
    tests.forEach (test)->
      it "should conver #{test.input} to #{test.output}", (done)->
        output = helpersDate.inputToDate test.input,test.dayFirst
        # console.log 'test.output', test.output
        # console.log 'output', output

        should.equal output.getTime(), test.output.getTime()
        done()
  

  describe 'inputToDateNum',->
    tests=[
      {
        input:'Mon, 18 Dec 1995 17:28:35 GMT',
        output:19951218,
      },
      {
        input:'2013W065'
        output: 20130208
      },
      {
        input:'20130208T080910'
        output:20130208,
      }
      {
        input:'2013-02-08 09:30:26.123-07:00'
        tz:'America/Phoenix'
        output: 20130208,
      },
      {
        input:'9/28/2021'
        output: 20210928,
      },
      {
        input:'28/9/2021'
        output: 20210928,
      },
      {
        input:'28/19/2021'
        output: '28/19/2021',
      },
      {
        input:'12/9/2021'
        dayFirst:true
        output: 20210912,
      },
      {
        input:'12/9/2021'
        dayFirst:false
        output: 20211209,
      }
      {
        input:'2020-10-20 03:44:20.000Z',
        output: 20201019,
      },
      {
        input:'2021-04-09 13:47:04.0'
        output: 20210409,
      },
      {
        input:'2020-10-19 00:00:00.0'
        output: 20201019,
      }
      {
        input:'/Date(1620273600000-0400)/',
        output: 20210506,
      },
      {
        input:20180122,
        output: 20180122,
      },
      {
        input: 1264118400000,
        output: 20100121,
      },
      {
        input:'2019-01-22'
        output: 20190122,
      }
    ]
    tests.forEach (test)->
      it "should conver to #{test.output} for #{test.input}", (done)->
        output = helpersDate.inputToDateNum test.input,test.dayFirst
        should.equal output, test.output
        done()


  describe 'daydiff',->
    tests=[ {
        start:20180122,
        end:20180123
        output: 1,
      },
      {
        start:20180122,
        end:20180122
        output: 0,
      },
      {
        start:'2018-12-22',
        end:20190122,
        output: 31,
      },
      {
        start:20181222,
        end:20190122,
        output: 31,
      },
    ]
    tests.forEach (test)->
      it "should compute correct day diff of  #{test.start} and #{test.end}", (done)->
        output = helpersDate.dayDiff test.start,test.end
        should.equal output, test.output
        done()
  
  describe 'OhzArrConve24HoursTo12Hours',->
    # 包含了测试 specialDealOhzTime 和 convert24HoursTo12Hours
    ohzArr=[
      {
        f:'2024-12-23 12:56:00',
        t:'2024-12-23 16:30:00'
      },
      {
        f:'20240122 04:00:00',
        t:'20240122 06:00:00'
      },
      {
        f:'2024/12/23 00:00:00',
        t:'2024/12/23 13:00:00',
      },
      {
        f:'24:00:00',
        t:'11:59:59',
      },
      {
        f:'13:00:00',
        t:'16:59:59',
      },
      {
        f:'2024-12-23',
        t:'23/12/2024'
      },
      {
        f:'2460:60',
        t:'11:6060',
      },
      {
        f:'23:60:60',
        t:'11:60:60',
      },
      {
        f: '2024-12-23 25:25',
        t: '2024-12-23 25:25:56',
      },
      {
        f: 'test string'
      },
      {
        t: 'test string'
      }
    ]
    result = [
      {
        f:'2024-12-23 12:56:00 PM',
        t:'2024-12-23 4:30:00 PM'
      },
      {
        f:'20240122 4:00:00',
        t:'20240122 6:00:00 AM'
      },
      {
        f:'2024/12/23 0:00:00',
        t:'2024/12/23 1:00:00 PM',
      },
      {
        f:'0:00:00',
        t:'11:59:59 AM',
      },
      {
        f:'1:00:00 PM',
        t:'4:59:59 PM',
      },
      {
        f:'2024-12-23',
        t:'23/12/2024',
      },
      {
        f:'',
        t:'',
      },
      {
        f:'11:59:59 PM',
        t:'11:59:59 AM',
      },
      {
        f:'',
        t:'',
      },
      {
        f: '',
      },
      {
        t: '',
      }
    ]
    it 'conve 24Hours To 12Hours', (done)->
      output = helpersDate.OhzArrConve24HoursTo12Hours ohzArr
      should.deepEqual(output, result)
      done()

  describe 'getWeeklyDateRange',->
    tests=[
      {
        input:'2024-01-14', # 周日
        description: 'Sunday input should return previous week',
        expectedStart: new Date('2024-01-01 00:00:00'),
        expectedEnd: new Date('2024-01-07 23:59:59.999')
      },
      {
        input:'2024-01-15 12:00:00', # 周一
        description: 'Monday input should return previous week',
        expectedStart: new Date('2024-01-08 00:00:00'),
        expectedEnd: new Date('2024-01-14 23:59:59.999')
      },
      {
        input:'2024-01-16', # 周二
        description: 'Tuesday input should return previous week',
        expectedStart: new Date('2024-01-08 00:00:00'),
        expectedEnd: new Date('2024-01-14 23:59:59.999')
      },
      {
        input:'2024-01-17', # 周三
        description: 'Wednesday input should return previous week',
        expectedStart: new Date('2024-01-08 00:00:00'),
        expectedEnd: new Date('2024-01-14 23:59:59.999')
      },
      {
        input:'2024-01-18', # 周四
        description: 'Thursday input should return previous week',
        expectedStart: new Date('2024-01-08 00:00:00'),
        expectedEnd: new Date('2024-01-14 23:59:59.999')
      },
      {
        input:'2024-01-19', # 周五
        description: 'Friday input should return previous week',
        expectedStart: new Date('2024-01-08 00:00:00'),
        expectedEnd: new Date('2024-01-14 23:59:59.999')
      },
      {
        input:'2024-01-20', # 周六
        description: 'Saturday input should return previous week',
        expectedStart: new Date('2024-01-08 00:00:00'),
        expectedEnd: new Date('2024-01-14 23:59:59.999')
      },
      {
        input:'invalid-date',
        description: 'Invalid date should return [null, null]',
        expectedStart: null,
        expectedEnd: null
      },
      {
        input:null,
        description: 'not date should return [null, null]',
        expectedStart: null,
        expectedEnd: null
      }
    ]
    
    tests.forEach (test)->
      it "#{test.description} for input #{test.input}", (done)->
        [start, end] = helpersDate.getWeeklyDateRange test.input
        
        if test.expectedStart is null
          should.equal start, null
          should.equal end, null
        else
          should.equal start.getTime(), test.expectedStart.getTime()
          should.equal end.getTime(), test.expectedEnd.getTime()
        done()