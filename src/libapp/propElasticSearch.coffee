helpers = require '../lib/helpers'
os = require 'os'
#try INCLUDE
geolib = require 'geolib'

elasticsearch = require '../lib/elasticsearch_async'
{
  createIndex,
  removeIndex,
  create<PERSON>lias,
  remove<PERSON>lias,
  checkIndexExist,
  upsertDocument,
  bulkInsertDocuments,
  bulkOperation,
  deleteDocById,
  getDocById,
  getIndexDocCount,
  putMapping,
  putSetting,
  searchIndexByBody,
  countDocuments
} = elasticsearch

{getQueryReadable} = require './propSearchReadable'
{isMlNum,getPtypeAbbr,getPropSearchSort} = require '../libapp/properties'
{getProvAbbrName} = require '../lib/cityHelper'
{inputToDate,inputToDateNum,dateFormat} = require '../lib/helpers_date'
{stringify,isLatitude,isLongitude} = require '../lib/helpers_string'
debugHelper = require '../lib/debug'
{isMlsPrefix,getThumbUrlFromPhoUrls,isPropObjectId} = require './properties'
debug = debugHelper.getDebugger()
use3rdPic = false
imgServerDlAddr = ''

module.exports._config_sections = ['elastic','serverBase','fileBase']
exports._config = _config = (cfg)->
  # debug.info 'load es with cfg:',cfg
  elasticsearch._config(cfg)
  use3rdPic = cfg?.serverBase?.use3rdPic
  imgServerDlAddr = cfg?.fileBase?.imgServerDlAddr
getIndexMapping = ()->
  # mapping = getMappingFromFile ver
  {
    "mappings": {
      "dynamic": false,
      "properties": mappingFields
    }
  }

getIndexSetting = (isImport)->
  if isImport
    # 提高import效率
    return {
      "refresh_interval":-1,
      "number_of_replicas":"0",
      "index.translog.durability":"async",
      "index.translog.sync_interval":"30s"
    }
  else
    # default settings
    return {
      "refresh_interval":"1s",
      # "number_of_replicas":"1", health yellow when 1
      "index.translog.durability":"REQUEST",
      "index.translog.sync_interval":"5s"
    }

LISTING_AGENT_FIELD = {
  type:'nested',
  properties:{
    nm:{
      type:'keyword'
    },
    tel:{
      type:"keyword"
    },
    id:{
      type:'keyword'
    },
    _id:{
      type:'keyword',
      index:true
    }
    agnt:{
      type:'nested',
      properties:{
        nm:{
          type:'keyword'
        },
        tel:{
          type:"keyword"
        },
        id:{
          type:'keyword'
        },
        _id:{
          type:'keyword',
          index:true
        }
      }
    }
  }
}

# 计算score的权重
SCORE_WEIGHT = {
  merge:  100
  ptype2: 40
  src:    30
  ptype:  20
  unt:    10
}

exports.mappingFields = mappingFields = {
  BrokerReciprocity:{
    type: 'keyword'
  },
  loc: {
    type: 'geo_point',
    index: true
  },
  ptype:{#ptype is change to 'r, b o' in import
    type:'keyword',
    index: true
  },
  ptype2:{
    type:'keyword',
    index: true
  },
  saletp:{
    type:'keyword',
    index:true
  },
  status:{
    type:'keyword',
    index:true
  },
  mfee:{
    type: 'integer',
    index:true
  }
  src:{
    type:'keyword',
    index:true
  },
  lst:{
    type:'keyword',
    index:true
  },
  addr:{
    type:'keyword',
  },
  st_num:{ # use to findAddrHistory, search by location
    type:'keyword',
    index:true
  },
  searchAddr:{ #use to do addr search, contain unt, in lowercase
    type:'keyword',
    index:true
  },
  city:{
    type:'keyword',
    index:true,
  },
  origCity:{
    type:'keyword'
  },
  origCmty:{
    type:'keyword'
  },
  origProv:{
    type:'keyword'
  },
  unt:{
    type:'keyword'
  },
  origUnt:{
    type:'keyword'
  },
  prov:{
    type:'keyword',
    index:true,
  },
  cmty:{
    type:'keyword',#may search in autocomplete
    index:true,
  },
  # TODO: long_range?
  lp:{
    type:'integer',
    index:true
  },
  lpr:{
    type:'integer',
    index:true
  },
  sp:{
    type:'integer',
    index:true
  },
  id:{
    type:'keyword',
    index:true
  },
  uid:{
    type:'keyword'
  },
  sid:{
    type:'keyword',
    index:true
  },
  aSIDs:{
    type:'keyword',
    index:true
  },
  dom:{
    type:'integer',
    index:true
  },
  bdrms:{
    type:'integer',
    index:true
  },
  tbdrms:{
    type:'integer',
  },
  br_plus:{
    type:'integer',
    index:true
  },
  bthrms:{
    type:'integer',
    index:true
  },
  tbthrms:{
    type:'integer',
    index:true
  },
  gr:{
    type:'integer',
    index:true
  },
  tgr:{
    type:'integer',
    index:true
  },
  score:{
    type:'integer',
    index:true
  },
  bsmt:{
    type:'keyword',
    index:true
  },
  mt:{
    type:'date',
    index:true,
    format: 'strict_date_optional_time||epoch_second'
  },
  ts:{
    type:'date',
    index:true,
    format: 'strict_date_optional_time||epoch_second'
  },
  dlTs:{
    type:'date',
    format: 'strict_date_optional_time||epoch_second'
  },
  spcts:{
    type:'date',
    index:true,
    format: 'strict_date_optional_time||epoch_second'
  },
  best_fir:{
    type:'integer',
    index:true
  },
  hgh_fir:{
    type:'integer',
    index:true
  },
  ele_fir:{
    type:'integer',
    index:true
  },
  trnst_dist:{
    type:'integer',
    index:true
  },
  sldDom:{
    type:'integer',
    index:true
  },
  sldd:{
    type:'integer',
    index:true
  },
  pho:{
    type:'integer',
    index:true
  },
  phosrc:{
    type:'keyword'
  },
  thumbUrl:{
    type:'keyword'
  },
  tnLH:{
    type:'keyword'
  },
  phoP:{
    type:'keyword'
  },
  ListingKey:{
    type:'keyword'
  },
  photonumbers:{
    type:'integer'
  },
  phodl:{
    type:'date',
    format: 'strict_date_optional_time||epoch_second'
  },
  PhotoDlDate:{
    type:'date',
    format: 'strict_date_optional_time||epoch_second'
  },
  phomt:{
    type:'date',
    format: 'strict_date_optional_time||epoch_second'
  },
  pc:{
    type:'integer',
    index:true
  },
  pcts:{
    type:'date',
    format: 'strict_date_optional_time||epoch_second'
  },
  sqft1:{
    type:'integer',
    index:true
  },
  sqft2:{
    type:'integer',
    index:true
  },
  rmSqft:{
    type:'integer',
    index:true
  },
  ltp:{
    type:'keyword',
    index:true
  },
  ptp:{
    type:'keyword'
  },
  pstyl:{
    type:'keyword'
  },
  stp:{
    type:'keyword'
  },
  _sysexp:{
    type:'date',
    format: 'strict_date_optional_time||epoch_second'
  },
  market_st:{
    type:'keyword',
    index:true
  },
  cmstn:{
    type:'keyword',
    index:true
  },
  uaddr:{
    type:'keyword',
    index:true
  },
  topup_pts:{
    type:'integer',
    index:true
  },
  vc:{#for home page recent props sort by vc
    type:'integer',
    index:true
  },
  topTs: {
    type:'date',
    format: 'strict_date_optional_time||epoch_second'
  },
  topMt: {
    type:'date',
    format: 'strict_date_optional_time||epoch_second'
  },
  lstd:{
    type:'date',
    format: 'strict_date_optional_time||epoch_second'
  },
  ld:{
    type:'integer'
  },
  onD:{
    type:'integer'
  },
  offD:{
    type:'integer'
  },
  topuids:{
    type:'keyword'
  },
  merged:{
    type:'text',
    index:true
  },
  ohz:{
    type:'nested',
    properties:{
      f:{
        type:'keyword',
        index:true
      },
      t:{
        type:'keyword',
        index:true
      },
      tp:{
        type:'keyword',
        index:true
      }
    }
  },
  pic:{
    type:'object'
  },
  picUrl:{ #for bre img display
    type:'text'
  },
  tax:{
    type:'float'
  },
  taxyr:{
    type:'integer'
  },
  lpunt:{
    type:'keyword'
  },
  ytvid:{
    type:'keyword'
  },
  vurlcn:{
    type:'keyword'
  },
  vimgcn:{
    type:'keyword'
  },
  showAddr:{
    type:'keyword'
  },
  apt_num:{
    type:'keyword'
  },
  daddr:{
    type:'keyword'
  },
  ddfID:{
    type:'keyword'
  },
  rmdaddr:{
    type:'keyword'
  },
  rmdpho:{
    type:'keyword'
  },
  dpred:{
    type:'keyword'
  },
  trbtp:{
    type:'keyword'
  },
  zip:{
    type:'keyword'
  },
  geoq:{
    type:'integer'
  },
  hideInfo:{
    type:'boolean'
  },
  useTrebPhoto:{
    type:'boolean'
  },
  his:{
    type:'nested'
  },
  bltYr1:{
    type:'integer',
    index:true
  },
  bltYr2:{
    type:'integer',
    index:true
  },
  rltr:{
    type:'keyword'
    # index:true
  }
  rmBltYr:{
    type:'integer',
    index:true
  },
  la:LISTING_AGENT_FIELD,
  la2:LISTING_AGENT_FIELD,
  aid:{
    type:'keyword',
    index:true
  },
  bid:{
    type:'keyword',
    index:true
  },
  slddt:{
    type:'date',
    format: 'strict_date_optional_time||epoch_second'
  },
  ctrdt:{
    type:'integer',
  },
  isPOS:{
    type:'integer',
    index:true
  },
  isEstate:{
    type:'integer',
    index:true
  },
  poss_date:{
    type:'keyword',
    index:true
  },
  front_ft:{
    type:'keyword'
  },
  depth:{
    type:'keyword'
  },
  front_ft_sqft:{
    type:'double'
  },
  depth_sqft:{
    type:'double'
  },
  lotsz_code:{
    type:'keyword'
  },
  irreg:{
    type:'keyword'
  },
  rmPsn:{
    type:'keyword'
    index:true
  },
  rmPsnDate:{
    type:'keyword',
    index:true
  },
  market_isv:{
    type:'boolean'
  },
  fce:{
    type: 'keyword'
  },
  bltYr:{
    type:'integer'
  },
  blcny:{
    type:'keyword'
  },
  age:{
    type:'keyword'
  },
  sqft:{
    type:'keyword'
  },
  m:{
    type:'text',
    index:true
  },
  park_spcs:{
    type:'integer'
  },
  sstp: {
    type:'keyword'
  },
  sptp: {
    type:'keyword'
  },
  market_rmProp:{
    type:'keyword'
  },
  sortOrder:{
    type:'integer'
  },
  m_zh:{
    type:'text'
  },
  # filter by roles
  del:{
    type:'integer'
  },
  lsp:{
    type:'integer'
  }
  lspDifPct:{
    type:'double'
  }
  llpDifPct:{
    type:'double'
  }
  private:{
    type:'boolean'
  },
  orgId:{
    type:'text'
  },
  phoIDs:{
    type:'text'
  },
  resoSrc:{
    type:'keyword'
  },
  MlsStatus:{
    type:'keyword'
  }
  # TODO: gen amenity from prop or 3rd party
  # amenity:{
  #   type:'text'
  # }
  # la,la2,aid,bid, -> search by angent id, @Me @branch or userAdmin
  # origCity,origCmty,origProv,origUnt -> sql/mongo old has(60e3d37d5c6130d4f548fce44ea29ea5981f0698), if is import fied, dont put in field base
  # picUrl          -> bre picUrl of pic for display
  # ctrdt+slddt(DOM)
  # his[]-> edmProperty
  # bltYr1,bltYr2,  -> adv search built year fields
  # rmBltYr -> calclated yr
}

exports.toUpdateMappingFields = toUpdateMappingFields = {
  # use to do minor upgrade
  # add additional fields
  properties:{
    
  }
}

exports.updateMapping = ({mappingVer})->
  body = toUpdateMappingFields
  index = "properties_#{mappingVer}"
  await putMapping {index,body}

# 修改index settings属性
# @param {boolean} isImport - flag of importing
exports.updateIndexSetting = ({mappingVer,isImport})->
  isImport = isImport or false
  settings = getIndexSetting isImport
  body = {settings}
  index = "properties_#{mappingVer}"
  await putSetting {index,body}

exports.PROPERTY_INDEX = PROPERTY_INDEX = 'properties'
exports.SYSDATA_ID = SYSDATA_ID = "propertiesToElastic@#{os.hostname}"
getIndiceAlias = (indice)->
  "#{indice}_alias"

exports.ES_MAPPING = 'ES_MAPPING'

BEST_SCHOOL = 9
FAST_SLD_DOM = 10
MAX_INT = 2147483647

###
@param {object} param - object of limit, bbox, page
@return {object} result - object that has size, skip
@return {number} object.size
@return {number} object.skip
###
getSizeSkip = (param)->
  {limit,bbox,page,psize} = param
  skip = 0
  if limit is false
    size = 0
  else
    size = if bbox then 50 else 20
  try
    size = parseInt(psize or limit) if (psize or limit)
    page = parseInt(page)
  catch e
    debug.error 'getSizeSkip ES error: ', e, limit
    size = if bbox then 50 else 20
  # finally
  #   size = 50
  # NOTE:  [illegal_argument_exception] Reason: [from] parameter cannot be negative but was [-50]
  if not isNaN(page)
    skip = Math.max(0,page * size)
  {size,skip}

###
@param {object} center lat, lng, order
@return {object} sortQuery - query to sort props
###
getDistanceSort = ({centerLat, centerLng,order})->
  return {
    '_geo_distance' : {
      'loc' : [centerLng, centerLat],
      'order' : order,
      'unit' : 'm',
      'mode' : 'min',
      'distance_type' : 'arc',
      'ignore_unmapped': true
    }
  }

###
# @description 根据传入的想要排序的方式，得到es排序规则的相应语句
# @param {string|object} sort - 根据什么排序，eg: 'dist-desc' 或 {ts:-1}
# @param {Boolean} isRM - 是否是rm房源
# @param {float} centerLat - 纬度
# @param {float} centerLng - 经度
# @return {array} sortQuery - 符合es规则的排序语句
###
exports.getSort = getSort =({sort,topTs,isRM = false, centerLat, centerLng})->
  if topTs
    #if top listing, sort by topTs:-1
    return [{topTs:{order:'desc'}}]
  sortQuery = []
  if sort
    distSort = null
    if 'string' is typeof sort
      sortArr = sort.split(',')
      for item in sortArr
        #sort by geo distance
        if (/dist/.test item) and centerLat and centerLng #TO confrim
          sortType = item.split '-'
          order = if sortType[1] is 'asc' then 'asc' else 'desc'
          distSort = getDistanceSort({centerLat, centerLng, order})
        else
          sortText = getPropSearchSort item
          sortType = sortText.split '-'
          if sortType[0] is 'auto'
            if isRM
              sortQuery.push({spcts:{order:'desc'}})
            else
              sortQuery.push({ts:{order:'desc'}})
          else
            sortObj = {}
            sortObj[sortType[0]] = {order:if sortType[1] is 'asc' then 'asc' else 'desc'}
            sortQuery.push(sortObj)
    else if 'object' is typeof sort
      for field, direction of sort
        if field is 'dist' and centerLat and centerLng
          order = if direction is -1 then 'desc' else 'asc'
          distSort = getDistanceSort({centerLat, centerLng, order})
        else
          sortObj = {}
          sortObj[field] = {order: if direction is -1 then 'desc' else 'asc'}
          sortQuery.push(sortObj)
    else
      return throw new Error('ES Sort must be string or object')
    if distSort # 距离排序默认放在最前面
      sortQuery.unshift(distSort)
  else
    sortQuery.push({spcts:{order:'desc'}})
  return sortQuery

###
@param {object} param - object of city, cmty, ptype
@return {array} queries - [{term:{prov:'ON'}},{term:{city:'toronto'}},{term:{ptype:'r'}}]
###
getTextMatchQueries = (param)->
  textFields = ['city','cmty','uaddr']
  queries = []
  for field in textFields
    if fieldVal = param[field]
      fieldVal = fieldVal[0] if Array.isArray fieldVal #handle cmty is array from saved search
      term = {}
      # NOTE: cmty检索需要忽略大小写
      if field is 'cmty'
        term = {cmty:{value:fieldVal,case_insensitive:true}}
      else
        term[field] = fieldVal
      queries.push {term}
  if (ptype = param.ptype) and (param.src isnt 'rm')
    ptype = if param.src is 'rm' then 'r' else getPtypeAbbr ptype
    queries.push {term:{ptype}}
  if prov = param.prov
    prov = getProvAbbrName prov
    queries.push {term:{prov}}
  return queries

###
@param {string} bsmt - basement
@return {object} - {term:{bsmt:'N/A'}}
###
getBsmtQuery = (bsmt)->
  return null unless bsmt
  return {term:{bsmt}}

###
@param {string} rltr - brokerage
@return {object} - {term:{rltr:'N/A'}}
es的正则表达式必需匹配整个令牌才干获得匹配所以不需要以^开始
es的正则中不能使用\w,\W之类的简写字符类，需要使用[^0-9a-zA-z]
前面的字符完全匹配之后，结尾需要使用.*来匹配其他字符
###
getRltrQuery = (rltr)->
  return null unless rltr
  strAry = rltr.split(/[ \.\,\s\(\)]/)
  reminingStrAry = []
  for str in strAry
    if str not in ['','INC','BROKERAGE','LTD']
      reminingStrAry.push str
  regStr = reminingStrAry.join('[^0-9a-zA-z]+')
  regStr += '.*'
  return {regexp:{rltr:regStr}}

###
@param {string} psn
@return {object} - {term:{rmPsn:'tba'}}
###
getPsnQuery = (psn)->
  return null unless psn
  # imm/immed/immd
  if /imm/.test psn
    psn = 'imm'
  return {term:{rmPsn:psn}}

###
@param {string/array} ptype2
# TODO: 应该遵循typescript的规范，ptype2应该是一个数组only
@return {object} - {term:{ptype2:'House'/['House','Apartment']}}
###
getPtype2Query = (ptype2)->
  ptype2Query = null
  return ptype2Query unless ptype2?.length
  # https://discuss.elastic.co/t/whats-the-difference-between-terms-and-term-with-bool-should/285186
  if Array.isArray ptype2
    ptype2Query = {terms:{ptype2}}
  else
    ptype2Query = {term:{ptype2}}
  return ptype2Query

###
@param {date} oh - open house date
@return {object} - {nested:{path:'ohz','query':{range:{'ohz.t':{gte:$date,lte:$date}}}}}
###
getOpenHouseQuery = (oh)->
  if oh and (oh not in ['0','false','null','undefined'])
    ts = new Date()
    start = helpers.dateFormat(ts,'YYYY-MM-DD')
    rangeOh = {'ohz.t':{gte:start}}
    if typeof oh is 'string'
      endDay = helpers.dateFormat(new Date(oh),'YYYY-MM-DD')
      rangeOh['ohz.t'] = {gte: start,lte: endDay}

    return {"nested":{
      "path":"ohz",
      "query":{
        "range":rangeOh
      }
    }}

###
@param {object} param - object of ts, sldd, oh, topTs
@return {array} timeQueries - array of range query
###
getTimeQueries = (param)->
  timeQueries = []
  {tsDiff,ts,sldd,oh,topTs,spcts,mt} = param
  if topTs
    timeQueries.push {range:{topTs:{gte:new Date()}}}
  if ts is 'new' and tsDiff
    #tag search from discover page
    timeQueries.push {range:{ts:{gte:new Date(Date.now() - tsDiff)}}}
  else if ts and (typeof ts isnt 'number') #edm new props, ts might be 20221022
    timeQueries.push {range:{ts:{gte:ts}}}
  if sldd #edm sold props
    timeQueries.push {range:{sldd:gte:sldd}}
  if oh
    timeQueries.push getOpenHouseQuery oh
  if spcts #findListingsByUserHistory
    timeQueries.push {range:{spcts:gt:spcts}}
  if mt #findListingsByUserHistory
    timeQueries.push {range:{mt:gt:mt}}
  timeQueries = null if (timeQueries.length is 0)
  return timeQueries

getPossDateQuery = (param)->
  dateQuery = null
  if param.min_poss_date or param.max_poss_date
    dateQuery = {bool:{should:[]}}
    possDateQuery = {range:{poss_date:{}}}
    rmPsnDateQuery = {range:{rmPsnDate:{}}}
    if param.min_poss_date
      possDateQuery.range.poss_date.gte = dateFormat(param.min_poss_date,'YYYYMMDD')
      rmPsnDateQuery.range.rmPsnDate.gte = dateFormat(param.min_poss_date,'YYYYMMDD')
    if param.max_poss_date
      possDateQuery.range.poss_date.lte = dateFormat(param.max_poss_date,'YYYYMMDD')
      rmPsnDateQuery.range.rmPsnDate.lte = dateFormat(param.max_poss_date,'YYYYMMDD')
    dateQuery.bool.should.push possDateQuery
    dateQuery.bool.should.push rmPsnDateQuery
  return dateQuery

### @param
type param {
  depth_f:number
  depth_t:number
}
###
getLotDepthFtQueries = ({depth_f,depth_t})->
  # console.log '=======depth',depth_f,depth_t
  lotDepthQuery = null
  if depth_f or depth_t
    lotDepthQuery = {range:{depth_sqft:{}}}#{bool:{must:[]}}
    if depth_f
      # lotDepthQuery.bool.must.push {range:{depth:{gte:depth_f}}}
      lotDepthQuery.range.depth_sqft.gte = depth_f
    if depth_t
      # lotDepthQuery.bool.must.push {range:{depth:{lte:depth_t}}}
      lotDepthQuery.range.depth_sqft.lte = depth_t
    # lotDepthQuery = {bool:{should:[lotDepthQuery]}}
  return lotDepthQuery

### @param
type param {
  frontFt_f:number
  frontFt_t:number
}
###
getLotFrontFtQueries = ({frontFt_f,frontFt_t})->
  lotFrontFtQuery = null
  if frontFt_f or frontFt_t
    lotFrontFtQuery = {range:{front_ft_sqft:{}}}#{bool:{must:[]}}
    if frontFt_f
      # lotDepthQuery.bool.must.push {range:{depth:{gte:depth_f}}}
      lotFrontFtQuery.range.front_ft_sqft.gte = frontFt_f
    if frontFt_t
      # lotDepthQuery.bool.must.push {range:{depth:{lte:depth_t}}}
      lotFrontFtQuery.range.front_ft_sqft.lte = frontFt_t
    # lotDepthQuery = {bool:{should:[lotDepthQuery]}}
  return lotFrontFtQuery

###
@param {object} param - object of bdrms, bthrms, gr
@return {array} roomQueries - array of room range query
###
getRoomQueries = (param)->
  roomQueries = []
  rooms = ['bdrms','bthrms','gr']
  for room in rooms
    if not (roomVal = param[room])
      continue
    # bthrms 筛选改用 tbthrms
    if room is 'bthrms'
      room = 'tbthrms'
    #prcess 1+,2+
    if ('string' is typeof roomVal) and (/^\d\+/.test roomVal)
      # search room=1,room_plus >= 1
      if (room is 'bdrms') and (/\d\+n/.test roomVal)
        roomVal = parseInt roomVal.slice(0,-1)
        match = {}
        match[room] = roomVal
        roomQueries.push {match}
        range = {}
        range['br_plus'] = {gte:1}
        roomQueries.push {range}
      else
        roomVal = parseInt roomVal.slice(0,-1)
        range = {}
        range[room] = {gte:roomVal}
        roomQueries.push {range}
    else
      roomVal = parseInt roomVal
      match = {}
      match[room] = roomVal
      roomQueries.push {match}
  roomQueries = null if (roomQueries.length is 0)
  return roomQueries

###
@param {object} param - object of max_mfee, no_mfee
@return {object} mfeeQuery - object of mfee query
###
getMfeeQuery = ({max_mfee,no_mfee})->
  mfeeQuery = null
  if max_mfee? or no_mfee
    max_mfee = if no_mfee then 0 else parseFloat(max_mfee)
    if max_mfee > 0
      max_mfee = MAX_INT if max_mfee > MAX_INT
      mfeeQuery = {range:{mfee:{lte:max_mfee}}}
    else # no MaintenanceFee
      mfeeQuery = {term:{mfee:0}}
  return mfeeQuery

###
@param {string} saletp - saletp -> 'Sale/Lease'
@return {object} - object of saletp query
###
getSaletpQuery = (saletp)->
  return null unless saletp
  if saletp.toLowerCase() is 'sale'
    saletp = 'Sale'
  else
    saletp = 'Lease'
  return {term: {saletp}}

###
@param {object}  - object of min_lp, max_lp, saletp
@return {object} - object of price range query
###
getPriceRangeQuery = ({min_lp,max_lp,saletp,status,soldOnly})->
  if (status is 'U' and soldOnly)
    lpLpr = 'sp'
  else
    lpLpr = if saletp?.toLowerCase() is 'lease' then 'lpr' else 'lp'
  lpQuery = null
  if min_lp or max_lp
    lpQuery = {range:{}}
    lpQuery.range[lpLpr] = {}
    if min_lp
      min_lp = MAX_INT if min_lp > MAX_INT
      lpQuery.range[lpLpr].gte = min_lp
    if max_lp
      max_lp = MAX_INT if max_lp > MAX_INT
      lpQuery.range[lpLpr].lte = max_lp
  return lpQuery

###
@param bbox
@return {object} -isSmallRegion, showDotAs
###
isSmallRegion = (bbox=[])->
  ret = {isSmallRegion:false}
  delta = bbox[3]-bbox[1]
  if(delta < 0.0065) #small unclickable dot on map @allen
    ret.isSmallRegion = true
    ret.showSoldAs = 'smallDot'
  if(delta < 0.0050)
    ret.isSmallRegion = true
    ret.showSoldAs = 'dot'
  if (delta < 0.0030)
    ret.showSoldAs = 'marker'
  return ret

###
@param bbox
@return {boolean} -isValidBbox
###
isValidBbox = (bbox)->
  return false unless bbox
  bbox ?= []
  return false unless bbox.length is 4
  return false unless bbox[0] and bbox[1] and bbox[2] and bbox[3]
  for i,idx in bbox
    bbox[idx] = parseFloat(i) if 'string' is typeof i
  for i in bbox
    return false if isNaN(i)
  # 验证经纬度是否有效
  # bbox格式为[minLng, minLat, maxLng, maxLat]
  return false unless isLongitude(bbox[0]) # minLng
  return false unless isLatitude(bbox[1]) # minLat
  return false unless isLongitude(bbox[2]) # maxLng
  return false unless isLatitude(bbox[3]) # maxLat
  # return false unless bbox[0] < bbox[2] and bbox[1] < bbox[3]
  return true


###
@param {array}  - bbox
@return {object} - object of bbox query
###
getBboxQuery = (bbox,isSoldLayer)->
  return null unless bbox
  # TODO: ignore this search or change bbox?
  return null unless isValidBbox(bbox)
  if isSoldLayer and not isSmallRegion(bbox).isSmallRegion
    # bbox = []
    return throw new Error('Region too big for sold layer')
  return {"geo_bounding_box":{
    "loc":{
      "top_left":{
        "lat": bbox[3],
        "lon": bbox[0]
      },
      "bottom_right":{
        "lat": bbox[1],
        "lon": bbox[2]
      }
    }
  }}
# @input {array} - array of polygon points, [lat,lng,lat,lng...]
# @output {array} - array of array, [[lng,lat],[lng,lat]...]
parseBndsToESFormat = (bnds)->
  bnds = bnds.map (i,idx)->
    if idx%2 is 0
      # NOTE: es的geo_polygon查询，需要将经纬度转成number，数据库存储的为string
      return [parseFloat(bnds[idx+1]),parseFloat(i)]
    return null
  bnds = bnds.filter (i)-> return i isnt null
  bnds


###
@params {array} - array of polygon points, [lat,lng,lat,lng...]
@return {object} - object of polygon query in ES
###
getPolygonQuery = (bnds)->
  return null unless bnds
  return null unless helpers.isValidPolygonBnds(bnds)
  bnds = parseBndsToESFormat bnds
  return {"geo_polygon":{
    "loc":{
      "points":bnds
    }
  }}

###
@param {object}  - object of sq_f, sq_t
@return {object} sqftQueries - object of sqft range query
###
getSqftQuery = ({sq_f,sq_t})->
  sqftQueries = null
  if sq_f or sq_t
    sqft12Query = {bool:{must:[]}}
    rmSqftQuery = {bool:{must:[]}}
    if sq_f
      sqft12Query.bool.must.push {range:{sqft1:{gte:sq_f}}}
      rmSqftQuery.bool.must.push {range:rmSqft:{gte:sq_f}}
    if sq_t
      sqft12Query.bool.must.push {range:{sqft2:{lte:sq_t}}}
      rmSqftQuery.bool.must.push {range:rmSqft:{lte:sq_f}}
    sqftQueries = {bool:{should:[sqft12Query,rmSqftQuery]}}
  return sqftQueries

###
@param {object}  - object of yr_f, yr_t
@return {object} sqftQueries - object of build year range query
###
getBuildYearQuery = ({yr_f,yr_t})->
  buildYearQuery = null
  if yr_f or yr_t
    thisYear = new Date().getFullYear()
    buildYear12Query = {bool:{must:[]}}
    rmBuildYearQuery = {bool:{must:[]}}
    if yr_f
      yr_f = parseInt yr_f,10
      min = thisYear-yr_f
      buildYear12Query.bool.must.push {range:{bltYr2:{lte:min}}}
      rmBuildYearQuery.bool.must.push {range:{rmBltYr:{lte:min}}}
    if yr_t
      yr_t = parseInt yr_t,10
      max  = thisYear-yr_t
      buildYear12Query.bool.must.push {range:{bltYr1:{gte:max}}}
      rmBuildYearQuery.bool.must.push {range:{rmBltYr:{gte:max}}}
    buildYearQuery = {bool:{should:[buildYear12Query,rmBuildYearQuery]}}
  return buildYearQuery

###
@param {string} sch - school string
@return {object} schoolQuery - object of school fir range query
###
getSchoolQuery = (sch)->
  schoolQuery = null
  return schoolQuery unless sch
  if sch is 'best'
    schoolQuery = {range:{'best_fir':{gte:BEST_SCHOOL}}}
    # use hgh_fir,ele_fir for future specific search
  return schoolQuery

###
@param {string} neartype - near type
@return {object} nearTypeQuery - object of transit distance range query
###
getnearTypeQuery = (neartype)->
  nearTypeQuery = null
  return nearTypeQuery unless neartype
  if neartype is 'mtr'
    nearTypeQuery = {range:{trnst_dist:{lte:1000}}}
  return nearTypeQuery

###
@param {string} lpChg - listing price change type
@return {object} lpChgQuery - object of price change range query
###
getLpChgQuery = (lpChg)->
  lpChgQuery = null
  return lpChgQuery unless lpChg
  if lpChg is 'off'
    lpChgQuery = {range:{pc:{lt:0}}}
  return lpChgQuery

###
@param {string} sold - sold type
@return {object} soldQuery - object of sold fast range query
###
getSoldFastQuery = (sold)->
  soldQuery = null
  return soldQuery unless sold
  if sold is 'fast'
    soldQuery = {range:{sldDom:{lte:FAST_SLD_DOM}}}
  return soldQuery

###
@param {string} recent - recent type
@return {object} soldQuery - object of recent sold range query
###
getRecentSoldQuery = (recent)->
  soldQuery = null
  return soldQuery unless recent
  if recent is 'sold'
    soldQuery = {range:{sldDom:{gte:0}}}
  return soldQuery

###
# NOTE: mongo prop_parts does not support remarks search yet!!!
@param {string} remarks - prop.m
@return {object}
###
getRemarksQuery = (remarks='')->
  remarksQuery = []
  return remarksQuery unless remarks
  # if not Array.isArray remarks
  #   if /\,/.test remarks
  #     remarks = remarks.split(',')
  #   else
  #     remarks = [remarks]
  # for r in remarks
  #   r = r.trim()
  #   remarksQuery.push {
  #     term:{
  #       m:r
  #         # query:remarks,
  #         # operator:'AND'
  #     }
  #   }
  remarks = remarks.toString()
  remarks = remarks.replace(/,/g,' ')
  remarks = helpers.escapeSpecialCharacter remarks
  # remarksQuery.push {
  #   term:{m:remarks.trim()}
  # }
  remarksQuery.push {
    query_string:{
      query:remarks.trim(),
      default_field:'m',
      default_operator:'AND'
    }
  }
  return remarksQuery

###
@param {object} param - dom,src,isAllowedVipUser,soldOnly,saletp
@return {object} result - object of status, matches queries
###
EMPTY_LIST = [0,'',null,undefined]
isValidDom = (dom,domYear,saleDesc)->
  return (dom? and (dom isnt '')) or \
    (domYear? and (domYear isnt '')) or \#dom might be 0
    (dom in EMPTY_LIST and (saleDesc in ['Sold','Leased']))

isSoldCondition = (isSoldLayer,dom,domYear,saleDesc)->
  return isSoldLayer or \
    (dom < 0) or (domYear < 0) or \
    (dom in EMPTY_LIST and (saleDesc in ['Sold','Leased']))

getDomQueries = (param)->
  {dom,domYear,src,isAllowedVipUser,soldOnly,saletp,saleDesc,type} = param
  result = {status:'',domMatches:[]}
  saletp ?= ''
  # console.log '+++++',dom,domYear,type,saleDesc,isValidDom(dom,domYear,saleDesc)
  if isValidDom(dom,domYear,saleDesc)
    fld = 'ts'
    # matches Sold-Any conditon, 2 years
    if saleDesc in ['Sold','Leased'] and dom in EMPTY_LIST
      dom = -720
    dom = parseInt dom
    domYear = parseInt domYear
    isSoldLayer = type is 'soldLayer'
    # console.log isSoldLayer,isSoldCondition(isSoldLayer,dom,domYear,saleDesc)
    if isSoldCondition(isSoldLayer,dom,domYear,saleDesc)
      fld = 'spcts'
      result.status = 'U'
      # NOTE: Business logic: src trb for non vip
      # Remore logic because other boards' props need to be searched: (BRE)
      # if (src is 'mls') and (not isAllowedVipUser)
      #   result.domMatches.push {term:{src:'TRB'}}
      if soldOnly
        if saletp.toLowerCase() is 'lease'
          result.domMatches.push {term:{lst:'Lsd'}}
        else
          result.domMatches.push {terms:{lst:['Sld','Pnd','Cld']}}
      return result if isSoldLayer
    domRange = {}
    ts = new Date()
    # NOTE: @allen, 2019+sale = 2019 < ts < 2020
    # 2019+sold = 2019 < spcts < 2020
    if domYear = Math.abs(domYear)
      targetYearTs = new Date(domYear+1,0,0)
      lastTargetYearTs = new Date(domYear,0,0)
      domRange[fld] = {gte:lastTargetYearTs,lte:targetYearTs}
    else
      domDay = Math.abs dom
      ts = new Date(ts.getFullYear(),ts.getMonth(),ts.getDate())
      ts = new Date(ts.getTime() - (domDay * 24 * 3600000))
      domRange[fld] = {gte:ts}
    
    result.domMatches.push {range:domRange}
  return result

###
@param {object} param - object of src, ltp, ltps, cmstn, rmProp
@return {object} {rmListingQuery,mustNotQuery} - array of rmListing query and mustNot query
###
getRMListingQuery = ({src,ltp,ltps,cmstn,rmProp})->
  # do not search src=RM prop when mls mode
  unless (src?.toLowerCase() is 'rm')
    return null #{rmListingQuery:[],mustNotQuery:{term:{src:'RM'}}}
  rmListingQuery = [{term:{src:'RM'}}]
  if ltp
    rmListingQuery.push {term:{ltp}}
  if ltps and (Array.isArray ltps)
    #handle edm rmlisting ltps:['assignment','exlisting']
    ltpsQuery = {terms:{ltp:ltps}}
    rmListingQuery.push ltpsQuery
  if ltp is 'rent'
    if cmstn
      mustNotQuery = {term:{cmstn:''}}
    else
      rmListingQuery.push {term:{cmstn:''}}
  # Trusted Assignment Listing and MLS homepage Trusted assignments  only show rmProp != null
  if rmProp
    rmListingQuery.push {exists:{field:'market_rmProp'}}
  return {rmListingQuery,mustNotQuery}

# 1281 Baker Valley Rd,0 Huntbach Ln,216229 Conc 4,402129 144 St E
isAddress = (str='')->
  if /\s(Rd|Line|W)$/.test str
    return true
  return /\d\s\w+\s?/.test str

# 210950,********,ld0152708,K19002222a
isSid = (sid)->
  # ********,1241222,22001860,SK884481
  if /^[a-zA-Z]\d{3,}$/.test sid
    return true
  sid = sid.toLowerCase()
  if isAddress(sid)
    return false
  return (/^([a-z]+)?\d{5,}/.test sid)
  # return (/^([a-z]+)?\d+/.test sid)

filterPropIds = (ids)->
  # test case: ********,1241222,22001860,SK884481,TRBC5501338
  stringIDs = [] #_id TRBW/BRE
  rmids = []  #id, RM1-xxx
  sids = [] #sid, W12345
  objectIds = []
  for id in ids
    id = id.toString().trim()
    continue unless id #id might be '' if search txt is '********,'
    lid = id.toLowerCase()
    if /^RM/.test id
      rmids.push id
    else if isMlsPrefix(id)
      stringIDs.push id.toUpperCase()
    else if isSid(id)
      sids.push id.toUpperCase()
    else if helpers.isObjectIDString(id)
      objectIds.push id.toLowerCase()
    else #mongoid
      stringIDs.push id.toLowerCase()
  [stringIDs,rmids,sids,objectIds]

###
@param {array} propIds - array of prop ids ['RM1-XXX','N2938456','8ad8f8ad8f8a8dsf8a']
@param {object} - object of es query
###
getMultiplePropIdsQuery = (propIds,searchMergeProps,searchDelProps)->
  if not Array.isArray propIds
    return []
  [stringIDs,rmids,sids,objectIds] = filterPropIds propIds
  # 构建should查询条件
  shouldQuery = []
  if stringIDs.length
    shouldQuery.push({ids:{values:stringIDs}})
  if rmids.length
    shouldQuery.push({terms:{id:rmids}})
  if sids.length
    # shouldQuery.push({terms:{sid:sids}})
    shouldQuery.push({terms:{aSIDs:sids}})
  if objectIds.length
    shouldQuery.push({terms:{_id:objectIds}})

  # 将should条件包装在一个bool查询中
  result = {
    bool:{
      must:[{bool:{should:shouldQuery}}]
    }
  }
  must_not = []
  unless searchMergeProps
    must_not.push {exists:{field:'merged'}}
  unless searchDelProps
    must_not.push {exists:{field:'del'}}
  if must_not.length
    result.bool.must_not = must_not
  return result

# if (isMlNum(searchTxt) or (helpers.isObjectIDString(searchTxt.split(',')[0]))) and (searchTxt.indexOf(',')>5 or Array.isArray searchTxt) #from share page
isMLSIdOrIDArray = (searchTxt='')->
  if not searchTxt.includes(',')
    return false
  idsArr = searchTxt.split(',')
  #from share page
  if (searchTxt.indexOf(',') > 0) and (searchTxt.indexOf(',') < 5)
    return false
  for id in idsArr
    # 用户可能输入 1234 addr, city, provb
    if helpers.isObjectIDString(id)
      return true
    if isMlNum(id)
      return true
    if /^RM1-\d+/.test id
      return true
  return false

###
@param {string} searchTxt - search input text
@return {object} - object of wildcard query
###
###
test case: ********,1241222,22001860,SK884481,TRBC5501338,RM1-51235
test case: 4750 Yonge St
test case: 357 4750 Yonge St
test case: ********
###
getTextSearchQuery = ({
  searchTxt,
  city,
  agentField,
  agentValue,
  searchMergeProps,
  searchDelProps,
  isAssignAdmin,
  fmtSearchTxt
})->
  if agentField and agentValue
    agentFieldLevel = agentField.split('.')
    path = agentFieldLevel[0]
    if agentFieldLevel.length is 3
      parent1 = agentFieldLevel[0]+'.'+agentFieldLevel[1]
      agentQuery = {
        nested:{
          path:parent1
          query:{term:{}}
        }
      }
      agentQuery.nested.query.term[agentField] = agentValue
    if agentFieldLevel.length is 2
      agentQuery = {term:{}}
      agentQuery.term[agentField] = agentValue
    return {"nested":{
      "path":path,
      "query":agentQuery
    }}
  searchTxt = searchTxt+''
  # from share properties list page
  # NOTE:rm房源列表分享需要使用_id查询
  if isMLSIdOrIDArray(searchTxt)
    # 'E2121'.split(',') -> ['E2121']
    propIds = searchTxt.split(',')
    return getMultiplePropIdsQuery propIds,searchMergeProps,searchDelProps
  #handle search by id
  must_not = []
  searchId = searchTxt.toUpperCase()
  # @fred除了用_id找，其它都不要显示了。包括admin。
  idQuery = {
    bool:{
      must:[
        wildcard:{}
      ]
    }
  }
  # admin与vip可以搜索merged房源
  if not searchMergeProps
    must_not.push {exists:{field:'merged'}}
  if not searchDelProps
    must_not.push {exists:{field:'del'}}
  if must_not.length
    idQuery.bool.must_not = must_not

  # TODO: refactor ，与idArray查询 dry问题
  if /^RM/.test searchId
    field = 'id'
    unless isAssignAdmin
      idQuery.bool.must_not = if idQuery.bool.must_not then idQuery.bool.must_not.concat([{term:{private:true}}]) else [{term:{private:true}}]
  else if isMlsPrefix(searchId) # id不一定给全,用正则查询
    field = 'id'
  else if isPropObjectId(searchId)
    searchId = searchId.toLowerCase()
    field = '_id'
  else if isSid searchId
    # field = 'sid'
    field = 'aSIDs'
  if field
    idQuery.bool.must[0].wildcard[field] = {value:"#{searchId}*",boost:1.0}
    if field is '_id'
      idQuery.bool.must = [{terms:{_id:[searchId]}}]
    return idQuery
  #handle address search
  searchAddrQuery = {bool:{must:[]}}
  searchInput = "*#{searchTxt.toLowerCase()}*"
  if fmtSearchTxt
    searchAddrQuery.bool.must.push {bool:{should:[
      {bool:{must:[{wildcard:{searchAddr:{value:"*#{fmtSearchTxt.toLowerCase()}*",boost:3.0}}}]}},
      {bool:{must:[{wildcard:{searchAddr:{value:searchInput,boost:3.0}}}]}}
    ]}}
  else
    searchAddrQuery.bool.must.push {wildcard:{searchAddr:{value:searchInput,boost:3.0}}}
  if city
    searchAddrQuery.bool.must.push {term:{city}}
  # addr检索不过滤merged房源
  if must_not.length
    searchAddrQuery.bool.must_not = must_not
  return searchAddrQuery

###
@param {object} - object bbox
@return {array} filters - array of filters query
###
getFiltersQuery = ({bnds, bbox, centerLat, centerLng, dist, type})->
  filters = []
  isSoldLayer = type is 'soldLayer'
  # bnds and bbox are exclusive
  if (bnds and helpers.isValidPolygonBnds(bnds) and polygonQuery = getPolygonQuery bnds)
    filters.push polygonQuery
  else if bboxFilter = getBboxQuery bbox,isSoldLayer
    filters.push bboxFilter
  if centerLat and centerLng and dist
    geoQuery = getGeoQuery {centerLat, centerLng, dist}
    filters.push geoQuery
  return filters

getGeoQuery =  ({dist, centerLat, centerLng})->
  geoQuery = null
  return {
    'geo_distance': {
      'distance': dist+'m',
      'loc': {
        'lat': centerLat,
        'lon': centerLng
      }
    }
  }


###
@param {object} param - object param
@return {array} termsQueries - array of query using terms
###
getTermsQueries = (param)->
  #exactly keyword match
  termsQueries = []
  if (textMatchQueries = getTextMatchQueries param) and textMatchQueries.length
    for textMatchQuery in textMatchQueries
      termsQueries.push textMatchQuery
  if param.isPOS
    termsQueries.push {term:{isPOS:1}}
  if param.isEstate
    termsQueries.push {term:{isEstate:1}}
  if param.exposures
    if not Array.isArray param.exposures
      param.exposures = [param.exposures]
    termsQueries.push {terms:{fce:param.exposures}}
  if param.bsmt
    # supp old native where bsmt is '', instead of []
    if not (Array.isArray param.bsmt)
      param.bsmt += ''
      if param.bsmt.indexOf(',')>0
        param.bsmt = param.bsmt.split(',')
      else
        param.bsmt = [param.bsmt]
    termsQueries.push {terms:{bsmt:param.bsmt}}
  termsQueries = null if (termsQueries.length is 0)
  return termsQueries

###
@param {object} param - object of saletp,ptype2,bsmt
@return {array} matchesQueries - array of query using matches
###
getMatchesQueries = ({ids,saletp,ptype2,bsmt,psn,rltr})->
  # Matches filter
  matchesQueries = []
  if (ids?.length)
    matchesQueries.push {terms:{_id:ids}}

  if (saletpQuery = getSaletpQuery saletp)
    matchesQueries.push saletpQuery

  if ptype2Query = getPtype2Query ptype2
    matchesQueries.push ptype2Query
  
  # if bsmtQuery = getBsmtQuery bsmt
  #   matchesQueries.push bsmtQuery
  
  if rltrQuery = getRltrQuery rltr
    matchesQueries.push rltrQuery
  
  if psnQuery = getPsnQuery psn
    matchesQueries.push psnQuery
  matchesQueries = null if (matchesQueries.length is 0)
  return matchesQueries

###
@param {object} param - object
@return {array} rangeQueries - array of query using range keyword
###
getRangeQueries = (param)->
  rangeQueries = []

  if param.pho
    phoVal = if typeof param.pho is 'number' then param.pho else 0
    rangeQueries = rangeQueries.concat {range:{pho:{gte:phoVal}}}
  
  if param.geoq
    geoqVal = if typeof param.geoq is 'number' then param.geoq else 0
    rangeQueries = rangeQueries.concat {range:{geoq:{gte:geoqVal}}}

  if timeQueries = getTimeQueries param
    rangeQueries = rangeQueries.concat timeQueries
  
  if possDateQuery = getPossDateQuery param
    rangeQueries = rangeQueries.concat possDateQuery

  if roomQueries = getRoomQueries param
    rangeQueries = rangeQueries.concat roomQueries

  if frontFtQueries = getLotFrontFtQueries param
    rangeQueries.push frontFtQueries

  if depthFtQueries = getLotDepthFtQueries param
    rangeQueries.push depthFtQueries

  if mfeeQuery = getMfeeQuery param
    rangeQueries.push mfeeQuery
  
  if priceQuery = getPriceRangeQuery param
    rangeQueries.push priceQuery
  
  if buildYearQuery = getBuildYearQuery param
    rangeQueries = rangeQueries.concat buildYearQuery
  
  if sqftQueries = getSqftQuery param
    rangeQueries = rangeQueries.concat sqftQueries
  
  if schoolQuery = getSchoolQuery param.sch
    rangeQueries.push schoolQuery
  
  if nearTypeQuery = getnearTypeQuery param.neartype
    rangeQueries.push nearTypeQuery
  
  if lpChgQuery = getLpChgQuery param.lpChg
    rangeQueries.push lpChgQuery
  rangeQueries = null if (rangeQueries.length is 0)
  return rangeQueries

createFavSortQuery = (favSort) ->
  return null unless favSort # 如果 favSort 不存在，返回 null
  # 定义常量
  SOLD_LST_VALUES = ['Sld', 'Pnd', 'Cld', 'Lsd']
  # 根据 favSort 值构建查询
  switch favSort.toLowerCase()
    when 'sale'
      return {
        must: [
          { term: { status: 'A' } }
        ]
      }
    when 'sold'
      return {
        must: [
          { term: { status: 'U' } }
          { terms: { lst: SOLD_LST_VALUES } }
        ]
      }
    when 'delisted'
      return {
        must: [
          { term: { status: 'U' } }
        ]
        must_not: [
          { terms: { lst: SOLD_LST_VALUES } }
        ]
      }  
    else
      return null # 如果 favSort 值不在预期范围内，返回 null

###
@param {object} param - object
@return {object} query - object of elasticsearch query
###
buildESQuery = (param)->
  {isAllowedVipUser,src,saletp,min_lp,max_lp,sort,dom,domYear,soldOnly,saleDesc,searchMergeProps,searchDelProps} = param
  query = {}
  debug.debug 'param.searchTxt: ',param.searchTxt
  if param.searchTxt
    query = getTextSearchQuery(param)
    debug.debug query
    return query

  if param.propIds #for saved/view props
    if not Array.isArray param.propIds
      param.propIds = param.propIds+''
      param.propIds = param.propIds.split(',')
    propIdsMatch = getMultiplePropIdsQuery(param.propIds,searchMergeProps,searchDelProps)
    # 添加 favSort 查询条件
    if favSortQuery = createFavSortQuery(param.favSort)
      propIdsMatch.bool.must = propIdsMatch.bool.must.concat(favSortQuery.must) if favSortQuery?.must?.length
      propIdsMatch.bool.must_not = propIdsMatch.bool.must_not.concat(favSortQuery.must_not) if favSortQuery?.must_not?.length
    return propIdsMatch
    
  query.bool = {}
  matches = []
  must_not = []
  unless searchMergeProps
    must_not = [{exists:{field:'merged'}}]#filter out prop with merged field in general search
  
  unless searchDelProps
    must_not.push {exists:{field:'del'}}

  if param.notPropId
    must_not = must_not.concat([{term:{_id:param.notPropId}}])
  
  # 非assignAdmin权限的人不能看楼花的 private is true 的房源
  unless param.isAssignAdmin
    must_not = must_not.concat([{term:{private:true}}])

  statusMatch = {term:{'status':'A'}}

  if param.addr
    matches.push {
      wildcard: {
        searchAddr:{
          value: "*#{param.addr.toLowerCase()}*"
          boost: 3.0
        }
      }
    }

  if termsQueries = getTermsQueries param
    matches = matches.concat(termsQueries)
  
  if matchesQueries = getMatchesQueries param
    matches = matches.concat(matchesQueries)
  
  {status,domMatches} = getDomQueries param
  statusMatch.term.status = status if status
  matches = matches.concat(domMatches) if domMatches.length
    # 首页房源添加图片数量判断
  if param.discover
    matches.push({exists:{field:'pho'}})
  
  if soldQuery = getSoldFastQuery param.sold
    matches.push soldQuery
    statusMatch.term.status = 'U'
  
  if soldQuery = getRecentSoldQuery param.recent
    matches.push soldQuery
    statusMatch.term.status = 'U'
  
  if remarksQuery = getRemarksQuery param.remarks
    matches = matches.concat(remarksQuery) if remarksQuery.length
  
  statusMatch.term.status = 'U' if param.status?.toLowerCase() is 'u'

  param.status = statusMatch.term.status
  if rangeQueries = getRangeQueries param
    matches = matches.concat(rangeQueries)
  
  if rmQueries = getRMListingQuery param
    {rmListingQuery,mustNotQuery} = rmQueries
    # rm status A?
    statusMatch.term.status = 'A'
    for rmQuery in rmListingQuery
      matches.push rmQuery
    must_not.push mustNotQuery if mustNotQuery

  # dont show rm prop when mls/other
  # MAP/List MLS listings 加入 rmProp != null 的 RM assignments房源
  unless (src?.toLowerCase() is 'rm')
    if param.rmProp
      # query.bool.should = [{bool:{must_not:[{term:{src:'RM'}}]}},{bool:{must:[{term:{src:'RM'}},{exists:{field:'market_rmProp'}}]}}]
      # query.bool.minimum_should_match = 1
      # 上面注释的2句和下面这个查询结果从数据上看相等价
      matches.push({bool:{should: [{bool:{must_not:[{term:{src:'RM'}}]}},{bool:{must:[{term:{src:'RM'}},{exists:{field:'market_rmProp'}}]}}]}})
    else
      must_not.push {term:{src:'RM'}}

  if not param.ignoreStatus
    matches.push statusMatch

  # sold at loss房源筛选
  # status===A时 ignore soldLoss
  if (param.soldLoss is 'loss') and (statusMatch.term.status isnt 'A')
    matches.push {exists:{field:'lspDifPct'}}

  query.bool.must_not = must_not if must_not.length > 0
  
  if (filtersQuery = getFiltersQuery param) and filtersQuery.length
    matches = matches.concat(filtersQuery)
  query.bool.must = matches

  return query

buildSameBuildingShouldQuery = (param)->
  queryShould = []
  uaddrMatch = {bool:{must:[]}}
  geoMatch = {bool:{must:[]}}
  if param.uaddr
    uaddrMatch.bool.must.push {term:{uaddr:param.uaddr}}
    queryShould.push uaddrMatch

  if param.geoq
    geoqVal = if typeof param.geoq is 'number' then param.geoq else 0
    geoMatch.bool.must.push {range:{geoq:{gte:geoqVal}}}
  
  if param.centerLat and param.centerLng and param.dist
    geoQuery = getGeoQuery param
  if geoQuery
    geoMatch.bool.must.push geoQuery
    if param.st_num?
      geoMatch.bool.must.push {term:{st_num:param.st_num}}
    queryShould.push geoMatch
  return {bool:{should:queryShould}}

# check if query is wildcard query
# eg: {query:{bool:{must:[{wildcard:{searchAddr:{value:'*32 bottingham dr*'}}}]}}}
# return false or field name
isWildcardQuery = (query)->
  if not query?.bool?.must?.length
    return false
  for term in query.bool.must
    if term.wildcard
      return Object.keys(term.wildcard)[0]
  return false

# 获取wildcard query value并去除两端*
# return string
getWildcardVaule = (query,field)->
  if query?.bool?.must?.length
    for term in query.bool.must
      if term.wildcard?[field]
        return term.wildcard[field].value?.replace /^\*(.*)\*$/,'$1'
  return

# 对通配符(模糊)查询时,根据查询字段长度升序返回
# NOTE: 排序 1.match匹配度;2.Active优先;3.Sale优先;4.spcts倒序
# eg:32 bottingham dr 先返回 32 Nottingham Dr 后返回 232 Nottingham Dr
buildQueryAndSortBySimilarityWhenUseWildcardQeury = (srcQueryObj,field)->
  if not field
    debug.error 'invaild wildcard query',stringify(srcQueryObj, { depth: 10 })
    return srcQueryObj
  # TODO: use user location based search, 地址addr重复很常见，city重复也很常见
  # TODO: src,ptype,unt应该是写入时计算，地址match用score
  
  fieldVal = getWildcardVaule srcQueryObj.query,field
  tokens = fieldVal.toLowerCase().split(/\s+/)
  functions = [{
    script_score:
      script:
        id: 'search_by_wildcard'
        params:
          field: field
          prefix: fieldVal.toLowerCase()
  }]

  newQueryBody = {
    query:{
      # 重新计算_score,按新的_score排序
      function_score:{
        query:srcQueryObj.query # 原有query
        functions,
        boost_mode:'replace',
      }
    }
    # NOTE: ES sort will sort Array values from 4,3,2,1 instead of 1,2,3,4
    # NOTE: 可以看做按照第一大类排序后，针对第一大类排序其中的子类，第一大类顺序固定，子类之间调整排序
    sort:[
      {status:{order:'asc'}}, # A优先
      {saletp:{order:'desc'}},# Sale优先
      {_score:{order:'desc'}}, # score降序
      {spcts:{order:'desc'}}  # spcts降序
    ]
  }
  flds = ['from','_source','size']
  for fld in flds
    newQueryBody[fld] = srcQueryObj[fld] if srcQueryObj[fld]?
  return newQueryBody

exports.search = (param, cb)->
  {size,skip} = getSizeSkip param
  # Caused by:illegal_argument_exception: Result window is too large, from + size must be less than or equal to: [10000] but was [10050]. See the scroll api for a more efficient way to request large data sets. This limit can be set by changing the [index.max_result_window] index level setting.
  if (skip + size) >= 10000
    errMsg = 'Warning: ES query size > 10000!'
    debug.warn errMsg,param
    if cb
      return cb errMsg,{}
    else
      return {took:0,props:[],readable:{},q:param,cnt:0}
  try
    body = {
      from: skip,
      sort: getSort(param),
      query: buildESQuery param
    }
    if param.similarShould
      body.query.bool.must.push(buildSameBuildingShouldQuery param.similarShould)
    if param.fields
      body['_source'] = Object.keys(param.fields)
      # 当fields包含lat/lng但不包含loc时,添加loc字段
      # NOTE: 改用es后，可能会存在部分fields没有loc但是需要lat/lng的情况
      if (param.fields.lat or param.fields.lng) and (not param.fields.loc)
        body['_source'].push 'loc' if not body['_source'].includes 'loc'
    if param.queryShould
      if body.query.bool.must?.length
        body.query.bool.must.push param.queryShould
      else
        body.query.bool.must = [param.queryShould]
    if param.must?.length
      if body.query.bool.must?.length
        body.query.bool.must = body.query.bool.must.concat param.must
      else
        body.query.bool.must = param.must
    if param.must_not
      if body.query.bool.must_not
        body.query.bool.must_not = body.query.bool.must_not.concat param.must_not
      else
        body.query.bool.must_not = param.must_not
  catch err
    if not /Region\stoo\sbig/.test err.toString()
      debug.error err
    else
      debug.info 'Warning: ES query region too big for soldLayer!'
    if cb
      return cb null,{took:0,props:[],readable:{},q:param,cnt:0}
    else
      return {took:0,props:[],readable:{},q:param,cnt:0}
  if size
    body.size = size
  # NOTE: search_after not returning correct result, may need to use PIT, see: https://discuss.elastic.co/t/search-after-query-doesnt-return-correct-results-for-pagination/281999
  # if param.searchAfter
  #   delete body.from
  #   # delete body.size
  #   if Array.isArray(param.searchAfter)
  #     body.search_after = param.searchAfter
  #   else
  #     body.search_after = [param.searchAfter]
  # if body.from is 0
  #   delete body.from
  index = getIndiceAlias(PROPERTY_INDEX)
  # 模糊查询时,根据查询字段长度排序返回
  if wildcardField = isWildcardQuery body.query
    body = buildQueryAndSortBySimilarityWhenUseWildcardQeury body,wildcardField
  debug.debug 'search body',stringify(body, { depth: 10 })
  # searchIndexByBody {index,body},(err,res)->
  try
    res = await searchIndexByBody {index,body}
  catch err
    debug.error 'searchIndexByBody', stringify(param, { depth: 10 }), body, err
    if cb
      cb null,{took:0,props:[],readable,q:param,cnt:0}
    else
      return {took:0,props:[],readable,q:param,cnt:0}
  readable = getQueryReadable param
  took = res?.took
  hits = res?.hits
  items = hits?.hits or []
  totalCnt = hits?.total?.value or 0
  if skip is 0
    cntRemain = Math.max totalCnt-items.length,0
  else
    cntRemain = Math.max totalCnt-skip-items.length,0
  props = []
  # console.log 'search result cnt,took,hits',items.length,res.took,res.hits.total.value
  # console.log util.inspect(rets)

  for p in items
    # console.log p._id,p._score,p.sort,p._source.src,p._source.addr,p._source.showAddr
    prop = p._source
    prop._id = p._id
    prop.lat ?= prop.loc?.lat
    prop.lng ?= prop.loc?.lon
    prop.topMt = new Date(prop.topMt)
    delete prop.loc
    # list search not displayed la info, will show in detail
    delete prop.la
    delete prop.la2
    props.push prop
  if cnt = items.length #get last item sort for pagination
    searchAfter = items[cnt-1]?.sort
    # console.log 'search_After',items[cnt-1].sort
  if cb
    cb(null, {took,props,readable,q:param,cnt:totalCnt,searchAfter,cntRemain})
  else
    return {took,props,readable,q:param,cnt:totalCnt,searchAfter,cntRemain}

exports.getPropertyIndex = getPropertyIndex = (ver)->
  "#{PROPERTY_INDEX}_#{ver}"

exports.checkPropertyIndexExist = checkPropertyIndexExist = ({index,mappingVer}) ->
  propIndex = index or getPropertyIndex(mappingVer)
  await checkIndexExist {index:propIndex}

exports.createPropertiesIndex = ({index,mappingVer,isInit})->
  propIndex = index or getPropertyIndex(mappingVer)
  body = getIndexMapping()
  if isInit
    # 修改settings 提高import效率
    body.settings = getIndexSetting isInit
  await createIndex {index:propIndex,body}

exports.createPropertiesAlias = ({mappingVer})->
  index = getPropertyIndex(mappingVer)
  alias = getIndiceAlias(PROPERTY_INDEX)
  await createAlias {index,alias}

exports.getPropertiesCount = ()->
  index = 'properties_alias'
  await getIndexDocCount {index}

exports.removePropertiesAlias = ({mappingVer})->
  index = getPropertyIndex(mappingVer)
  name = getIndiceAlias(PROPERTY_INDEX)
  propIsExist = await checkPropertyIndexExist {index}
  unless propIsExist
    return
  await removeAlias {index,name}

exports.removePropertyIndex = ({index,mappingVer})->
  propIndex = index or getPropertyIndex(mappingVer)
  await removeIndex {index:propIndex}

exports.upsertProp = ({prop,mappingVer})->
  return unless prop
  id = prop._id.toString()
  index = if mappingVer then getPropertyIndex(mappingVer) else getIndiceAlias(PROPERTY_INDEX)
  body = getESProp {prop}
  await upsertDocument {id,index,body}

exports.bulkInsertProps = ({props,mappingVer})->
  return unless props?.length
  index = if mappingVer then getPropertyIndex(mappingVer) else getIndiceAlias(PROPERTY_INDEX)
  body = []
  for prop in props
    # offD在1970年之前的房源不写入es
    if prop.offD and ('number' isnt typeof prop.offD)
      debug.error 'invalid prop. bad offD.', prop.offD, prop
      continue
    _id = prop._id.toString()
    newProp = getESProp {prop}
    newProp._id = _id
    body.push newProp
  return unless body.length
  await bulkInsertDocuments {index,body}

###
  props: [{
    type :  string  [index,delete,update] # insert在elastic中为index
    _id : stirng
    prop? : object
  }]
###
exports.bulkOperationProps = ({props,mappingVer})->
  return unless props?.length
  index = if mappingVer then getPropertyIndex(mappingVer) else getIndiceAlias(PROPERTY_INDEX)
  body = []
  for item in props
    # offD在1970年之前的房源不写入es
    if item.prop
      if item.prop.offD and ('number' isnt typeof item.prop.offD)
        debug.error 'invalid item.prop. bad offD.', item.prop.offD, item.prop
        continue
      item.prop = getESProp {prop:item.prop}
    body.push item
  return unless body.length
  await bulkOperation {index,body}

exports.deleteProp = (p)->
  id = p._id.toString()
  index = getIndiceAlias(PROPERTY_INDEX)
  await deleteDocById {index,id}

###
@param {array} schools - array of school info
@return {object} {hghFir,eleFir} - object of high school fir, element school fir
###
getSchoolFields = (schools)->
  return null unless (schools?.length and Array.isArray(schools))
  hghFir = 0
  eleFir = 0
  for school in schools
    {ele,hgh,fir} = school
    if ele and fir and fir >= eleFir
      eleFir = fir
    if hgh and fir and fir >= hghFir
      hghFir = fir
  return {hghFir,eleFir}

###
@param {array} trnsts - array of transit info
@return {number} minDist - min transit distince
###
getTransitDistFields = (trnsts)->
  return null unless (trnsts?.length and Array.isArray(trnsts))
  minDist = 0
  for trnst in trnsts
    minDist = trnst.dist unless minDist
    if trnst.dist < minDist
      minDist = trnst.dist
  return minDist

###
@param {array} ohz - array of open house info
@return {array} esOhz - array of open house data in elasticsearch
###
getOhzField = (ohz)->
  #2021-07-25 16:30 -> 202107251630
  formatOh = (time)->
    return time.replace(/[\-\s\:]/g,'')
  esOhz = []
  for oh in ohz
    {f,t,tp} = oh
    newOh = {tp}
    newOh.f = formatOh(f) if f
    newOh.t = formatOh(t) if t
    esOhz.push newOh
  return esOhz

###
@param {object} - object of loc, lat, lng
@return {object} {lat,lon} - object of lat, lon
###
getLocField = ({loc,lat,lng})->
  if coor = loc?.coordinates
    [lon,lat] = coor
    return {lat,lon}
  else if (lat and lng)
    return {lat:lat,lon:lng}

###
@param {object} - object of property
@return {object} additionFields - object of additional fields in property
###
getESNewFields = ({prop})->
  {addr,unt,ptype2,saletp,schools,trnst,lpr,sldd,ptype,bndCmty,cmty,phomt,phodl,ld,PhotoDlDate,mt} = prop
  # if ld is date obj, parse it with inputToDateNum
  if ('string' is typeof ld) or (ld instanceof Date)
    prop.ld = inputToDateNum(ld)
  if 'string' is typeof PhotoDlDate
    prop.PhotoDlDate = new Date(PhotoDlDate)
  if not (mt instanceof Date)
    prop.mt = new Date(mt)
  searchAddr = if unt then "#{unt} #{addr}" else addr
  additionFields = {}
  additionFields.searchAddr = searchAddr.toLowerCase() if 'string' is typeof searchAddr
  if not prop.aSIDs?.length
    prop.aSIDs = [prop.sid]
    if prop.origSid? and (not /draft/i.test(prop.origSid))
      prop.aSIDs.push prop.origSid
  else
    prop.aSIDs = prop.aSIDs.map((item)->return item.sid)
  prop.aSIDs = Array.from(new Set(prop.aSIDs)) # 去重
  # front_ft and depth
  if prop.front_ft? and parseFloat prop.front_ft
    additionFields.front_ft_sqft = parseFloat prop.front_ft
  if prop.depth? and parseFloat prop.depth
    additionFields.depth_sqft = parseFloat prop.depth
  # remarks
  if prop.m
    prop.m = prop.m+''
    additionFields.m = prop.m.toLowerCase()
  prop.ptype = getPtypeAbbr ptype
  # NOTE: cmty and bndCmty could be different, eg.TRBW5755685, cmty=Hurontario,bndCmty=Streetsville
  prop.cmty = bndCmty?.nm or cmty
  prop.vc ?= 0
  # ESERROR: [mapper_parsing_exception] Reason: failed to parse field [lp] of type [integer] in document with id 'DDF25458572'. Preview of field's value: '2699900000'
  # NOTE: Math.pow(2,31)-1 == Max ES integer
  # Max js integer range == 2^53-1
  if parseInt(prop.lp) >= 2147483647
    prop.lp = 1
  unless prop.ts
    prop.ts = prop.ts or inputToDate(prop.onD) # if no ts use onD
  if typeof lpr is 'string'
    delete prop.lpr
    additionFields.lpr = prop.lp
  # onD is number
  if typeof prop.onD is 'string'
    delete prop.onD
  unless typeof sldd is 'number'
    delete prop.sldd
  additionFields.loc = getLocField prop
  if schools and schFir = getSchoolFields(schools)
    {hghFir,eleFir} = schFir
    additionFields.hgh_fir = hghFir
    additionFields.ele_fir = eleFir
    additionFields.best_fir = if hghFir >= eleFir then hghFir else eleFir
  if prop.pc and Math.abs(prop.pc) > MAX_INT
    delete prop.pc
  if prop.mfee and prop.mfee > MAX_INT
    prop.mfee = MAX_INT
  unless prop.mfee
    prop.mfee = 0
  # transit
  if trnst and dist = getTransitDistFields(trnst)
    additionFields.trnst_dist = dist
  additionFields.cmstn = prop.cmstn or ''
  if phomt and (typeof phomt is 'string')
    #fix 2016-07-02 08:57:42.0 -> 2016-07-02T08:57:42.0
    prop.phomt = new Date(phomt.replace(' ','T'))
  if phodl and (typeof phodl is 'string')
    prop.phodl = new Date(phodl.replace(' ','T'))
  if poss_date = prop.poss_date
    # 2022-02-13 00:00:00.0 -> 20220213
    if typeof poss_date is 'string'
      try
        poss_date = parseInt(dateFormat(poss_date.substr(0,10),'YYYYMMDD'))
      catch err
        debug.error 'parse poss_date fail', poss_date, err
    additionFields.poss_date = poss_date
  if market_isv = prop.market?.isV
    additionFields.market_isv = market_isv
  if market_rmProp = prop.market?.rmProp
    additionFields.market_rmProp = market_rmProp
    additionFields.sortOrder = prop.market.sortOrder if prop.market?.sortOrder
    additionFields.m_zh = prop.m_zh if prop.m_zh
    additionFields.m = prop.m if prop.m
  for field in ['schools','trnst','loc','lat','lng','market']
    delete prop[field]
  # TODO: ctrdt is ISODate for prop TRBW3589387, might be a bug in import
  if prop.ctrdt
    if (prop.ctrdt instanceof Date) and (not isNaN(prop.ctrdt))
      additionFields.ctrdt = parseInt(dateFormat(prop.ctrdt,'YYYYMMDD'))
    else if not isNaN(prop.ctrdt)
      # ctrdt is number
    else
      additionFields.ctrdt = parseInt prop.ctrdt
      if isNaN additionFields.ctrdt
        delete additionFields.ctrdt
        delete prop.ctrdt
  return additionFields

exports.allESFields = allESFields = Object.assign mappingFields,toUpdateMappingFields.properties

###
# @description 计算房产在Elasticsearch中的分数
# @param {object} prop - 房产对象
# @param {string} prop.src - 房产来源
# @param {string} prop.unt - 房产单元号
# @param {string} prop.ptype2 - 房产子类型
# @param {string} prop.ptype - 房产类型
# @return {number} score - 计算得出的房产分数
###
exports.calculatePropertyScore = calculatePropertyScore = (prop)->
  score = 0
  # 计算merge的分数,非merged 10, merged 0
  if not prop.merged
    score += 10 * SCORE_WEIGHT.merge
  
  # 计算ptype2的分数, 默认 10, locker 9, parking 5
  score += 10 * SCORE_WEIGHT.ptype2
  if /Locker/.test prop.ptype2
    score -= 1 * SCORE_WEIGHT.ptype2
  if /Parking/.test prop.ptype2
    score -= 5 * SCORE_WEIGHT.ptype2

  # 计算src的分数, DDF 5, 其他 10
  if /DDF/i.test prop.src
    score += 5 * SCORE_WEIGHT.src
  else
    score += 10 * SCORE_WEIGHT.src

  # 计算ptype的分数, Residential 10, 其它 5
  if prop.ptype is 'r'
    score += 10 * SCORE_WEIGHT.ptype
  else
    score += 5 * SCORE_WEIGHT.ptype

  # 计算unt的分数, condo/commerical时 没有unt -10
  hasUnit = prop.unt? and prop.unt?.toString().length > 0
  isCondo = /Condo|Parking|Other|Apartment/i.test prop.ptype2
  isCommercial = /o|b/i.test prop.ptype
  if (not hasUnit) and (isCondo or isCommercial)
    score -= 10 * SCORE_WEIGHT.unt

  return score

###
@param {object} - object of property
@return {object} newProp - object of property for elasticsearch
###
exports.getESProp = getESProp = ({prop}) ->
  return null unless prop
  additionFields = getESNewFields {prop}
  newProp = Object.assign prop,additionFields
  if prov = newProp.prov
    newProp.prov = getProvAbbrName prov

  # NOTE: add thumbUrl temporarily
  newProp.thumbUrl = getThumbUrlFromPhoUrls prop,use3rdPic,imgServerDlAddr

  # 防止房源存在bthrms没有tbthrms字段。eg: RM房源没有tbthrms字段
  newProp.tbthrms ?= newProp.bthrms if newProp.bthrms

  delete newProp._id
  for key in Object.keys(newProp)
    delete newProp[key] unless mappingFields[key]

  # calc score
  newProp.score = calculatePropertyScore(prop)
  newProp

exports.countDocuments = ({mappingVer,query})->
  return unless query
  index = if mappingVer then getPropertyIndex(mappingVer) else getIndiceAlias(PROPERTY_INDEX)
  ret = await countDocuments {index,query}
  return ret?.count

###
# @description 聚合查询ES文档
# @param {string} [mappingVer] - ES mapping版本号
# @param {object} query - 查询条件
# @param {object} aggs - 聚合条件
# @return {object} 聚合查询结果
###
exports.aggregateDocuments = ({mappingVer,query,aggs})->
  return unless query and aggs
  index = if mappingVer then getPropertyIndex(mappingVer) else getIndiceAlias(PROPERTY_INDEX)
  body = {
    size: 0
    query: query
    aggs: aggs
  }
  return await searchIndexByBody {index,body}
